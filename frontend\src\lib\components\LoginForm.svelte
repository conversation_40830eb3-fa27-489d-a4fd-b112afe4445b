<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { authStore } from '$lib/stores/auth'
  import { _ } from '$lib/i18n'

  const dispatch = createEventDispatcher()

  // Form data
  let formData = {
    email: '',
    password: ''
  }

  // Form state
  let isSubmitting = false
  let errors: Record<string, string> = {}
  let showPassword = false

  // Validation
  function validateForm() {
    errors = {}

    // Email validation
    if (!formData.email) {
      errors.email = $_('auth.email_required')
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = $_('auth.email_invalid')
    }

    // Password validation
    if (!formData.password) {
      errors.password = $_('auth.password_required')
    }

    return Object.keys(errors).length === 0
  }

  async function handleSubmit() {
    if (!validateForm()) {
      return
    }

    isSubmitting = true

    try {
      const result = await authStore.login({
        email: formData.email.trim(),
        password: formData.password
      })

      if (result.success) {
        dispatch('success', { customer: result.customer })
      } else {
        errors.general = result.error || $_('auth.login_failed')
      }
    } catch (error) {
      errors.general = error instanceof Error ? error.message : $_('auth.login_failed')
    } finally {
      isSubmitting = false
    }
  }

  function clearError(field: string) {
    if (errors[field]) {
      delete errors[field]
      errors = { ...errors }
    }
  }
</script>

<form on:submit|preventDefault={handleSubmit} class="space-y-4 text-xl">
  <!-- General Error -->
  {#if errors.general}
    <div class="p-4 rounded-md" style="background: #fee2e2; border: 1px solid #fecaca;">
      <p class="font-body text-sm" style="color: #dc2626;">
        {errors.general}
      </p>
    </div>
  {/if}

  <!-- Email -->
  <div>
    <label for="login-email" class="block font-body text-xl font-medium mb-2" style="color: var(--color-text-primary);">
      {$_('auth.email')}
    </label>
    <input
      id="login-email"
      type="email"
      bind:value={formData.email}
      on:input={() => clearError('email')}
      required
      class="w-full px-3 py-2 font-body border rounded-md focus:outline-none focus:ring-2 transition-colors"
      style="border-color: {errors.email ? '#dc2626' : 'var(--color-border)'}; background: var(--color-bg-primary); color: var(--color-text-primary); focus:ring-color: var(--color-text-accent);"
      placeholder={$_('auth.email_placeholder')}
      disabled={isSubmitting}
    />
    {#if errors.email}
      <p class="mt-1 font-body text-sm" style="color: #dc2626;">{errors.email}</p>
    {/if}
  </div>

  <!-- Password -->
  <div>
    <label for="login-password" class="block font-body text-xl font-medium mb-2" style="color: var(--color-text-primary);">
      {$_('auth.password')}
    </label>
    <div class="relative">
      <input
        id="login-password"
        type={showPassword ? 'text' : 'password'}
        bind:value={formData.password}
        on:input={() => clearError('password')}
        required
        class="w-full px-3 py-2 pr-10 font-body border rounded-md focus:outline-none focus:ring-2 transition-colors"
        style="border-color: {errors.password ? '#dc2626' : 'var(--color-border)'}; background: var(--color-bg-primary); focus:ring-color: var(--color-text-accent);"
        placeholder={$_('auth.password_placeholder')}
        disabled={isSubmitting}
      />
      <button
        type="button"
        on:click={() => showPassword = !showPassword}
        class="absolute inset-y-0 right-0 pr-3 flex items-center"
        disabled={isSubmitting}
      >
        <span class="text-xl" style="color: var(--color-text-secondary);">
          <img src="/images/eye.png" alt="Show password" class="h-6 w-auto" />
        </span>
      </button>
    </div>
    {#if errors.password}
      <p class="mt-1 font-body text-sm" style="color: #dc2626;">{errors.password}</p>
    {/if}
  </div>

  <!-- Submit Button -->
  <button
    type="submit"
    disabled={isSubmitting}
    class="w-full btn-primary flex items-center justify-center"
    class:opacity-50={isSubmitting}
  >
    {#if isSubmitting}
      <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
      {$_('auth.signing_in')}
    {:else}
      {$_('auth.sign_in')}
    {/if}
  </button>

  <!-- Registration Link -->
  <div class="text-center">
      <button
        type="button"
        on:click={() => dispatch('switch-to-register')}
        class="font-body underline hover:no-underline transition-all accent-text m-0"
        disabled={isSubmitting}
        style="color: var(--color-text-primary);"
      >
        {$_('auth.create_account')}
      </button>
  </div>

</form>
