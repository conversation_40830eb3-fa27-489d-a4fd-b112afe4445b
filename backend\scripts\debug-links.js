#!/usr/bin/env node

/**
 * Script to debug digital product links and relationships
 * Usage: node scripts/debug-links.js [product_variant_id]
 */

require('dotenv').config()

// Simple database query approach
const { Client } = require('pg')

async function debugLinks(productVariantId) {
  const client = new Client({
    connectionString: process.env.DATABASE_URL
  })

  try {
    console.log('🔍 Debugging digital product links...')
    await client.connect()

    // Check digital products
    console.log('\n📄 DIGITAL PRODUCTS:')
    let digitalProductsQuery = 'SELECT id, name, product_variant_id, created_at FROM digital_product'
    let queryParams = []

    if (productVariantId) {
      digitalProductsQuery += ' WHERE product_variant_id = $1'
      queryParams = [productVariantId]
    }

    const digitalProductsResult = await client.query(digitalProductsQuery, queryParams)

    if (digitalProductsResult.rows.length > 0) {
      digitalProductsResult.rows.forEach(dp => {
        console.log(`  📦 ${dp.name} (ID: ${dp.id})`)
        console.log(`     Variant ID: ${dp.product_variant_id}`)
        console.log(`     Created: ${dp.created_at}`)
        console.log('')
      })
    } else {
      console.log('  No digital products found')
    }

    // Check product variants
    console.log('\n🏷️ PRODUCT VARIANTS:')
    let variantsQuery = `
      SELECT pv.id, pv.title, pv.sku, pv.product_id, p.title as product_title
      FROM product_variant pv
      LEFT JOIN product p ON pv.product_id = p.id
    `
    let variantParams = []

    if (productVariantId) {
      variantsQuery += ' WHERE pv.id = $1'
      variantParams = [productVariantId]
    }

    const variantsResult = await client.query(variantsQuery, variantParams)

    if (variantsResult.rows.length > 0) {
      variantsResult.rows.forEach(variant => {
        console.log(`  🏷️ ${variant.title} (ID: ${variant.id})`)
        console.log(`     SKU: ${variant.sku}`)
        console.log(`     Product: ${variant.product_title} (${variant.product_id})`)
        console.log('')
      })
    } else {
      console.log('  No product variants found')
    }

    // Check for existing links and duplicates
    console.log('\n🔗 CHECKING FOR EXISTING LINKS:')
    const linkCheckQuery = `
      SELECT
        dp.id as dp_id,
        dp.name as dp_name,
        dp.product_variant_id,
        pv.title as variant_title,
        COUNT(*) OVER (PARTITION BY dp.product_variant_id) as duplicate_count
      FROM digital_product dp
      LEFT JOIN product_variant pv ON dp.product_variant_id = pv.id
      ORDER BY dp.product_variant_id, dp.created_at
    `

    const linkResult = await client.query(linkCheckQuery)

    if (linkResult.rows.length > 0) {
      linkResult.rows.forEach(row => {
        if (row.variant_title) {
          const status = row.duplicate_count > 1 ? '⚠️ DUPLICATE' : '✅ LINKED'
          console.log(`  ${status}: Digital Product "${row.dp_name}" ↔ Variant "${row.variant_title}"`)
          if (row.duplicate_count > 1) {
            console.log(`     (${row.duplicate_count} digital products for this variant)`)
          }
        } else {
          console.log(`  ❌ ORPHANED: Digital Product "${row.dp_name}" → Missing Variant ${row.product_variant_id}`)
        }
      })
    }

    // Check remote links table
    console.log('\n🌐 REMOTE LINKS:')
    try {
      const remoteLinksQuery = `
        SELECT * FROM remote_link
        WHERE (from_module = 'digitalProductModuleService' AND to_module = 'product')
           OR (from_module = 'product' AND to_module = 'digitalProductModuleService')
      `
      const remoteLinksResult = await client.query(remoteLinksQuery)

      if (remoteLinksResult.rows.length > 0) {
        console.log(`  Found ${remoteLinksResult.rows.length} remote links:`)
        remoteLinksResult.rows.forEach(link => {
          console.log(`    ${link.from_module} → ${link.to_module}`)
          console.log(`    From: ${link.from_id} → To: ${link.to_id}`)
        })
      } else {
        console.log('  No remote links found')
      }
    } catch (error) {
      console.log('  Could not check remote links:', error.message)
    }

  } catch (error) {
    console.error('❌ Error debugging links:', error)
  } finally {
    await client.end()
  }
}

// Get product variant ID from command line
const productVariantId = process.argv[2]
if (productVariantId) {
  console.log(`🔍 Filtering by product variant ID: ${productVariantId}`)
}

debugLinks(productVariantId)
