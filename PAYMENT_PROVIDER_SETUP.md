# Payment Provider Configuration Guide

This guide explains how to switch between Stripe and Tranzila payment providers in your MedusaJS application.

## How It Works

The application now supports **single payment provider mode** - only one payment provider is active at a time, controlled by the `PAYMENT_PROVIDER` environment variable.

## Switching Payment Providers

### Option 1: Use Tranzila (Default)

Set in your `backend/.env` file:
```bash
PAYMENT_PROVIDER=tranzila

# Tranzila Configuration
TRANZILA_SUPPLIER=your_supplier_id
TRANZILA_USER=your_user_name
TRANZILA_PASSWORD=your_password
TRANZILA_CURRENCY=ILS
TRANZILA_LANGUAGE=he
TRANZILA_TEST_MODE=true
TRANZILA_IFRAME_MODE=false
```

### Option 2: Use Stripe

Set in your `backend/.env` file:
```bash
PAYMENT_PROVIDER=stripe

# Stripe Configuration
STRIPE_API_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
```

## Frontend Configuration

The frontend automatically detects which payment provider is active and shows the appropriate payment form:

- **Tranzila**: Shows redirect or iframe payment options
- **Stripe**: Shows credit card form with Stripe Elements

## Testing

### Tranzila Test Cards
```
Success: ****************
Failure: ****************
Expiry: Any future date
CVV: Any 3 digits
```

### Stripe Test Cards
```
Success: ****************
Decline: ****************
Expiry: Any future date
CVV: Any 3 digits
```

## Switching Between Providers

1. **Stop the backend server** (Ctrl+C)
2. **Update the environment variable** in `backend/.env`:
   ```bash
   PAYMENT_PROVIDER=stripe  # or tranzila
   ```
3. **Restart the backend server**:
   ```bash
   cd backend
   yarn dev
   ```
4. **The frontend will automatically adapt** to show the correct payment form

## Production Deployment

For production, set the environment variable in your deployment platform:

### Azure App Service
```bash
az webapp config appsettings set --resource-group myResourceGroup --name myAppName --settings PAYMENT_PROVIDER=tranzila
```

### Docker
```bash
docker run -e PAYMENT_PROVIDER=tranzila your-app
```

### Environment Files
Create separate `.env.production` files for different environments:

**For Tranzila Production:**
```bash
PAYMENT_PROVIDER=tranzila
TRANZILA_TEST_MODE=false
TRANZILA_SUPPLIER=your_production_supplier_id
# ... other production settings
```

**For Stripe Production:**
```bash
PAYMENT_PROVIDER=stripe
STRIPE_API_KEY=sk_live_your_live_key
# ... other production settings
```

## Benefits of This Approach

1. **Clean Separation**: Only one payment provider loads at a time
2. **No Conflicts**: Eliminates potential conflicts between providers
3. **Easy Switching**: Change one environment variable to switch providers
4. **Environment Specific**: Different providers for dev/staging/production
5. **Automatic Frontend**: Frontend automatically adapts to active provider

## Troubleshooting

### Provider Not Loading
- Check that `PAYMENT_PROVIDER` is set correctly
- Verify the provider-specific environment variables are configured
- Restart the backend server after changing environment variables

### Frontend Shows Wrong Provider
- Clear browser cache and refresh
- Check browser console for API errors
- Verify backend is returning the correct payment providers

### CORS Issues
- Ensure `STORE_CORS` includes your frontend URL
- Check that only one origin is specified (no duplicates)

## Advanced Configuration

### Multiple Providers (Not Recommended)
If you need both providers active simultaneously, remove the conditional logic in `medusa-config.ts` and include both providers in the array. However, this is not recommended as it can cause confusion for customers.

### Region-Specific Providers
You can extend this approach to use different providers for different regions by adding region checks in the configuration logic.
