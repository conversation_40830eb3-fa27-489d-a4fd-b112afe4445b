import { browser } from '$app/environment'
import Cookies from 'js-cookie'

const API_BASE_URL = import.meta.env.VITE_BACKEND_URL || 'http://localhost:9000'
const PUBLISHABLE_API_KEY = import.meta.env.VITE_MEDUSA_PUBLISHABLE_API_KEY
const DEFAULT_REGION_ID = import.meta.env.VITE_DEFAULT_REGION_ID || 'reg_01JYBEH7CVEM96RQANAXK9ZCKV' // Israel region

// Debug: Log environment variables (in development and production for debugging)
if (typeof window !== 'undefined') {
  console.log('🔧 API Configuration:')
  console.log('  - Backend URL:', API_BASE_URL)
  console.log('  - Raw VITE_BACKEND_URL:', import.meta.env.VITE_BACKEND_URL)
  console.log('  - Publishable Key:', PUBLISHABLE_API_KEY ? `${PUBLISHABLE_API_KEY.substring(0, 10)}...` : 'NOT SET')
  console.log('  - Default Region ID:', DEFAULT_REGION_ID)
  console.log('  - All VITE env vars:', Object.keys(import.meta.env).filter(key => key.startsWith('VITE_')))
}

export interface ApiResponse<T> {
  data?: T
  error?: string
  message?: string
}

export interface Chapter {
  id: string
  title: string
  title_en?: string
  title_ru?: string
  preview_content?: string
  preview_content_en?: string
  preview_content_ru?: string
  content?: string
  difficulty_level?: string
  order_in_book: number
  language: string
  price?: number
  is_free: boolean
  reading_time_minutes?: number
  tags?: string[]
  tags_en?: string[]
  tags_ru?: string[]
  video_url?: string
  exercise_url?: string
  audio_url?: string
  created_at: string
  updated_at?: string
  // Legacy fields for compatibility
  chapter_number?: number
  estimated_reading_time?: number
  description?: string
  description_he?: string
  description_en?: string
}

export interface User {
  id: string
  email: string
  first_name: string
  last_name: string
  created_at: string
  updated_at: string
}

export interface Product {
  id: string
  title: string
  subtitle?: string
  description?: string
  handle: string
  is_giftcard: boolean
  status: 'draft' | 'proposed' | 'published' | 'rejected'
  thumbnail?: string
  weight?: number
  length?: number
  height?: number
  width?: number
  hs_code?: string
  origin_country?: string
  mid_code?: string
  material?: string
  collection_id?: string
  type_id?: string
  discountable: boolean
  external_id?: string
  created_at: string
  updated_at: string
  deleted_at?: string
  profile_id: string
  variants: ProductVariant[]
  options: ProductOption[]
  images: ProductImage[]
  tags: ProductTag[]
  type?: ProductType
  collection?: ProductCollection
  metadata?: Record<string, any>
}

export interface ProductVariant {
  id: string
  title: string
  product_id: string
  sku?: string
  barcode?: string
  ean?: string
  upc?: string
  variant_rank?: number
  inventory_quantity: number
  allow_backorder: boolean
  manage_inventory: boolean
  hs_code?: string
  origin_country?: string
  mid_code?: string
  material?: string
  weight?: number
  length?: number
  height?: number
  width?: number
  metadata?: Record<string, any>
  prices: ProductVariantPrice[]
  options: ProductOptionValue[]
  created_at: string
  updated_at: string
}

export interface ProductVariantPrice {
  id: string
  currency_code: string
  amount: number
  variant_id: string
  region_id?: string
  created_at: string
  updated_at: string
}

export interface ProductOption {
  id: string
  title: string
  product_id: string
  created_at: string
  updated_at: string
  values: ProductOptionValue[]
}

export interface ProductOptionValue {
  id: string
  value: string
  option_id: string
  variant_id: string
  created_at: string
  updated_at: string
}

export interface ProductImage {
  id: string
  url: string
  metadata?: Record<string, any>
  created_at: string
  updated_at: string
}

export interface ProductTag {
  id: string
  value: string
  created_at: string
  updated_at: string
}

export interface ProductType {
  id: string
  value: string
  created_at: string
  updated_at: string
}

export interface ProductCollection {
  id: string
  title: string
  handle: string
  created_at: string
  updated_at: string
}

class ApiClient {
  private baseUrl: string
  private authToken: string | null = null

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl
    if (browser) {
      this.authToken = Cookies.get('auth_token') || null

      // Additional debug info
      if (import.meta.env.DEV) {
        console.log('🔧 ApiClient initialized with:')
        console.log('  - Base URL:', this.baseUrl)
        console.log('  - Has Publishable Key:', !!PUBLISHABLE_API_KEY)
      }
    }
  }

  private getHeaders(includePublishableKey: boolean = false): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    }

    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`
    }

    // Add publishable key for store API endpoints
    if (includePublishableKey) {
      if (!PUBLISHABLE_API_KEY) {
        console.error('❌ VITE_MEDUSA_PUBLISHABLE_API_KEY is not set. Please add it to your .env.local file.')
        throw new Error('A valid publishable key is required to proceed with the request')
      }
      headers['x-publishable-api-key'] = PUBLISHABLE_API_KEY
    }

    return headers
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`

      console.log('Requesting:', url, endpoint)

      // Determine if this is a store API endpoint that needs publishable key
      const isStoreEndpoint = endpoint.startsWith('/store')
      console.log('Is store endpoint:', isStoreEndpoint)
      console.log('Headers:', this.getHeaders(isStoreEndpoint), options)
      const headers1 =  this.getHeaders(isStoreEndpoint);

      const response = await fetch(url, {
        ...options,
        headers: {
          ...headers1,
          ...options.headers,
        },
      })

      console.log('Response:', response);

      const data = await response.json();
      console.log('Data:', data);

      if (!response.ok) {
        // Handle 401 unauthorized errors
        if (response.status === 401) {
          console.log('🚨 API Client: 401 Unauthorized detected for:', endpoint)

          // Import and call auth store handler
          try {
            const { authStore } = await import('$lib/stores/auth')
            authStore.handleUnauthorized()
          } catch (error) {
            console.warn('Failed to handle unauthorized error:', error)
          }
        }

        return {
          error: data.error || data.message || 'Request failed',
        }
      }

      return { data }
    } catch (error) {
      return {
        error: error instanceof Error ? error.message : 'Network error',
      }
    }
  }

  // Chapters API
  async getChapters(): Promise<ApiResponse<{ chapters: Chapter[] }>> {
    return this.request('/public/chapters')
  }

  async getChapter(id: string): Promise<ApiResponse<{ chapter: Chapter }>> {
    return this.request(`/public/chapters/${id}`)
  }

  // Products API (Medusa.js)
  async getProducts(params?: {
    limit?: number
    offset?: number
    q?: string
    collection_id?: string[]
    type_id?: string[]
    tags?: string[]
    is_giftcard?: boolean
    created_at?: { lt?: string; gt?: string; gte?: string; lte?: string }
    updated_at?: { lt?: string; gt?: string; gte?: string; lte?: string }
    region_id?: string
  }): Promise<ApiResponse<{ products: Product[]; count: number; offset: number; limit: number }>> {
    const queryParams = new URLSearchParams()

    // Add region_id for pricing calculations (MedusaJS v2 requirement)
    // Only add if explicitly provided
    if (params?.region_id) {
      queryParams.append('region_id', params.region_id)
    }

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && key !== 'region_id') { // Skip region_id as we already added it
          if (Array.isArray(value)) {
            value.forEach(v => queryParams.append(key, String(v)))
          } else if (typeof value === 'object') {
            Object.entries(value).forEach(([subKey, subValue]) => {
              if (subValue !== undefined) {
                queryParams.append(`${key}[${subKey}]`, String(subValue))
              }
            })
          } else {
            queryParams.append(key, String(value))
          }
        }
      })
    }

    // Add pricing fields to get calculated prices (MedusaJS v2 requirement)
    queryParams.append('fields', '*variants.calculated_price')

    const endpoint = `/store/products${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request(endpoint)
  }

  async getProduct(id: string, regionId?: string): Promise<ApiResponse<{ product: Product }>> {
    const region = regionId || DEFAULT_REGION_ID
    return this.request(`/store/products/${id}?fields=*variants.calculated_price&region_id=${region}`)
  }

  async searchProducts(query: string, params?: {
    limit?: number
    offset?: number
    filter?: Record<string, any>
  }): Promise<ApiResponse<{ hits: Product[]; query: string; offset: number; limit: number; estimatedTotalHits: number }>> {
    const queryParams = new URLSearchParams({ q: query })
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          if (typeof value === 'object') {
            queryParams.append(key, JSON.stringify(value))
          } else {
            queryParams.append(key, String(value))
          }
        }
      })
    }

    return this.request(`/store/products/search?${queryParams.toString()}`)
  }

  // Health check
  async healthCheck(): Promise<ApiResponse<{ status: string; message: string; timestamp: string }>> {
    return this.request('/health')
  }

  // Test endpoint
  async testConnection(): Promise<ApiResponse<any>> {
    return this.request('/test/chapters')
  }

  // Store Settings API (custom public endpoint - no publishable key required)
  async getStoreSettings(): Promise<ApiResponse<{ settings: any }>> {
    return this.request('/public/settings')
  }

  // Regions API (to get available regions for pricing)
  async getRegions(): Promise<ApiResponse<{ regions: any[] }>> {
    return this.request('/store/regions')
  }

  // Collections API (for chapters organization)
  async getCollections(params?: {
    limit?: number
    offset?: number
    handle?: string[]
  }): Promise<ApiResponse<{ collections: any[]; count: number; offset: number; limit: number }>> {
    const queryParams = new URLSearchParams()

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          if (Array.isArray(value)) {
            value.forEach(v => queryParams.append(key, String(v)))
          } else {
            queryParams.append(key, String(value))
          }
        }
      })
    }

    const endpoint = `/store/collections${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request(endpoint)
  }

  async getCollection(id: string): Promise<ApiResponse<{ collection: any }>> {
    return this.request(`/store/collections/${id}`)
  }

  // Get products from a specific collection (chapters)
  async getCollectionProducts(collectionId: string, params?: {
    limit?: number
    offset?: number
    region_id?: string
  }): Promise<ApiResponse<{ products: Product[]; count: number; offset: number; limit: number }>> {
    const queryParams = new URLSearchParams()

    // Always add region_id for pricing calculations (MedusaJS v2 requirement)
    const regionId = params?.region_id || DEFAULT_REGION_ID
    queryParams.append('region_id', regionId)

    // Add pricing fields to get calculated prices
    queryParams.append('fields', '*variants.calculated_price')

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && key !== 'region_id') { // Skip region_id as we already added it
          if (Array.isArray(value)) {
            value.forEach(v => queryParams.append(key, String(v)))
          } else {
            queryParams.append(key, String(value))
          }
        }
      })
    }

    // Add collection_id filter (MedusaJS v2 way to get collection products)
    queryParams.append('collection_id', collectionId)

    const endpoint = `/store/products?${queryParams.toString()}`
    return this.request(endpoint)
  }

  // Authentication API (Customer Registration & Login)
  async registerCustomer(data: {
    email: string
    password: string
  }): Promise<ApiResponse<{ token: string }>> {
    return this.request('/auth/customer/emailpass/register', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  async loginCustomer(data: {
    email: string
    password: string
  }): Promise<ApiResponse<{ token: string }>> {
    return this.request('/auth/customer/emailpass', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  async createCustomer(data: {
    email: string
    first_name?: string
    last_name?: string
    phone?: string
  }, token: string): Promise<ApiResponse<{ customer: any }>> {
    return this.request('/store/customers', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(data)
    })
  }

  async getCustomer(token: string): Promise<ApiResponse<{ customer: any }>> {
    console.log('🔍 Getting customer with token:', token ? `${token.substring(0, 20)}...` : 'NO TOKEN')

    const response = await this.request('/store/customers/me', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })

    console.log('👤 Customer response:', response.data ? 'SUCCESS' : 'FAILED', response.error || '')
    return response as ApiResponse<{ customer: any }>
  }



  async logoutCustomer(token: string): Promise<ApiResponse<any>> {
    return this.request('/auth/session', {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
  }

  // Cart API methods
  async createCart(data: {
    region_id?: string
    sales_channel_id?: string
  }): Promise<ApiResponse<{ cart: any }>> {
    return this.request('/store/carts', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  async transferCartToCustomer(cartId: string, token: string): Promise<ApiResponse<{ cart: any }>> {
    return this.request(`/store/carts/${cartId}/customer`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
  }

  // Transfer cart to authenticated customer (new v2.0.5+ API)
  async transferCart(cartId: string, token: string): Promise<ApiResponse<{ cart: any }>> {
    return this.request(`/store/carts/${cartId}/transfer`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
  }



  async getCart(cartId: string): Promise<ApiResponse<{ cart: any }>> {
    return this.request(`/store/carts/${cartId}`)
  }

  async addToCart(cartId: string, data: {
    variant_id: string
    quantity: number
  }): Promise<ApiResponse<{ cart: any }>> {
    return this.request(`/store/carts/${cartId}/line-items`, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  async removeFromCart(cartId: string, lineItemId: string): Promise<ApiResponse<{ cart: any }>> {
    return this.request(`/store/carts/${cartId}/line-items/${lineItemId}`, {
      method: 'DELETE'
    })
  }

  async updateCartItem(cartId: string, lineItemId: string, data: {
    quantity: number
  }): Promise<ApiResponse<{ cart: any }>> {
    return this.request(`/store/carts/${cartId}/line-items/${lineItemId}`, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  async updateCart(cartId: string, data: {
    region_id?: string
  }): Promise<ApiResponse<{ cart: any }>> {
    return this.request(`/store/carts/${cartId}`, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  // Metadata-specific API methods
  async getProductsMetadata(productIds?: string[]): Promise<ApiResponse<{ products: any[] }>> {
    const queryParams = new URLSearchParams()

    // Request only id and metadata fields
    queryParams.append('fields', 'id,metadata')

    // If specific product IDs are provided, filter by them
    if (productIds && productIds.length > 0) {
      productIds.forEach(id => queryParams.append('id', id))
    }

    const endpoint = `/store/products${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request(endpoint)
  }

  async getProductMetadata(productId: string): Promise<ApiResponse<{ product: any }>> {
    return this.request(`/store/products/${productId}?fields=id,metadata`)
  }

  // Customer management methods
  async updateCustomer(_customerId: string, data: {
    first_name?: string
    last_name?: string
    phone?: string
    metadata?: Record<string, any>
  }, token: string): Promise<ApiResponse<{ customer: any }>> {
    return this.request(`/store/customers/me`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(data)
    })
  }

  async getCustomerOrders(token: string, params?: {
    limit?: number
    offset?: number
    fields?: string
  }): Promise<ApiResponse<{ orders: any[] }>> {
    const queryParams = new URLSearchParams()

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, String(value))
        }
      })
    }

    // Include order items and product information
    if (!params?.fields) {
      queryParams.append('fields', '*items,*items.product')
    }

    const endpoint = `/store/orders${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.request(endpoint, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
  }

  async getOrder(orderId: string, token: string): Promise<ApiResponse<{ order: any }>> {
    return this.request(`/store/orders/${orderId}?fields=*items,*items.product`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
  }

  // Checkout and Payment Methods
  async createPaymentCollection(cartId: string): Promise<ApiResponse<{ payment_collection: any }>> {
    const createResponse = await this.request(`/store/payment-collections`, {
      method: 'POST',
      body: JSON.stringify({
        cart_id: cartId
      })
    });

    console.log('🔍 Payment collection create response:', createResponse)

    return createResponse as ApiResponse<{ payment_collection: any }>
  }

  async initializePaymentSessions(paymentCollectionId: string, data?: {
    provider_id?: string
    data?: Record<string, any>
  }): Promise<ApiResponse<{ payment_collection: any }>> {
    return this.request(`/store/payment-collections/${paymentCollectionId}/payment-sessions`, {
      method: 'POST',
      body: JSON.stringify(data || {})
    })
  }

  async setPaymentProvider(paymentCollectionId: string, providerId: string): Promise<ApiResponse<{ payment_collection: any }>> {
    return this.request(`/store/payment-collections/${paymentCollectionId}/payment-sessions/${providerId}`, {
      method: 'POST'
    })
  }

  async getPaymentProviders(regionId?: string): Promise<ApiResponse<{ payment_providers: any[] }>> {
    const params = regionId ? `?region_id=${regionId}` : ''
    return this.request(`/store/payment-providers${params}`)
  }

  // Simplified method that follows MedusaJS v2 pattern
  async initiatePaymentSession(cart: any, data: {
    provider_id: string
    data?: Record<string, any>
  }): Promise<ApiResponse<{ payment_collection: any }>> {
    let paymentCollectionId = cart.payment_collection?.id

    // Step 1: Create payment collection if it doesn't exist
    if (!paymentCollectionId) {
      console.log('🔄 Creating payment collection for cart:', cart.id)
      const createResponse = await this.createPaymentCollection(cart.id)
      console.log('🔍 Payment collection create response:', createResponse)
      if (!createResponse.data?.payment_collection) {
        throw new Error('Failed to create payment collection')
      }
      paymentCollectionId = createResponse.data.payment_collection.id
      console.log('✅ Payment collection created:', paymentCollectionId)
    }

    // Step 2: Initialize payment sessions with provider
    console.log('🔄 Initializing payment session with provider:', data.provider_id)
    return this.request(`/store/payment-collections/${paymentCollectionId}/payment-sessions`, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  async completeCart(cartId: string): Promise<ApiResponse<{ type: 'cart' | 'order', cart?: any, order?: any, error?: string }>> {
    console.log('🔔 FRONTEND: Starting cart completion process')
    console.log('⏰ Timestamp:', new Date().toISOString())
    console.log('🛒 Cart ID:', cartId)

    // For digital products, we might need to add a shipping method first
    try {
      console.log('🚚 Checking for shipping options...')
      // Try to get available shipping options
      const shippingResponse = await this.getShippingOptions(cartId)
      if (shippingResponse.data?.shipping_options && shippingResponse.data.shipping_options.length > 0) {
        console.log('📦 Found shipping options:', shippingResponse.data.shipping_options.length)
        // Add the first available shipping option
        await this.addShippingMethod(cartId, {
          option_id: shippingResponse.data.shipping_options[0].id
        })
        console.log('✅ Added shipping method for cart completion')
      } else {
        console.log('ℹ️ No shipping options found')
      }
    } catch (error: any) {
      console.log('ℹ️ No shipping options available or already set:', error?.message || 'Unknown error')
    }

    console.log('🔄 Calling cart completion endpoint...')
    const startTime = Date.now()

    try {
      const response = await this.request(`/store/carts/${cartId}/complete`, {
        method: 'POST'
      })

      const duration = Date.now() - startTime
      console.log('✅ FRONTEND: Cart completion response received')
      console.log('⏱️ Duration:', duration + 'ms')
      console.log('📋 Response data:', JSON.stringify(response.data, null, 2))
      console.log('🔄 Response received successfully')

      console.log('=' .repeat(80))
      return response as ApiResponse<{ type: 'cart' | 'order', cart?: any, order?: any, error?: string }>
    } catch (error: any) {
      const duration = Date.now() - startTime
      console.error('❌ FRONTEND: Cart completion failed')
      console.error('⏱️ Duration:', duration + 'ms')
      console.error('🛒 Cart ID:', cartId)
      console.error('❌ Error:', error.message)
      console.error('=' .repeat(80))
      throw error
    }
  }

  async updateCartEmail(cartId: string, email: string): Promise<ApiResponse<{ cart: any }>> {
    return this.request(`/store/carts/${cartId}`, {
      method: 'POST',
      body: JSON.stringify({ email })
    })
  }

  async updateCartAddresses(cartId: string, data: {
    shipping_address?: any
    billing_address?: any
  }): Promise<ApiResponse<{ cart: any }>> {
    return this.request(`/store/carts/${cartId}`, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  async addShippingMethod(cartId: string, data: {
    option_id: string
    data?: Record<string, any>
  }): Promise<ApiResponse<{ cart: any }>> {
    return this.request(`/store/carts/${cartId}/shipping-methods`, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  async getShippingOptions(cartId: string): Promise<ApiResponse<{ shipping_options: any[] }>> {
    return this.request(`/store/shipping-options?cart_id=${cartId}`)
  }

  async createStripeCheckoutSession(cartId: string, data: {
    success_url: string
    cancel_url: string
  }): Promise<ApiResponse<{ checkout_session: any }>> {
    return this.request(`/store/carts/${cartId}/checkout-session`, {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  /*/ Tranzila payment methods
  async generateTranzilaPaymentUrl(paymentCollectionId: string, options?: {
    cart_id?: string
    amount?: number
    currency?: string
  }): Promise<ApiResponse<{
    payment_url: string
    session_id: string
    transaction_id: string
  }>> {
    return this.request('/store/tranzila/payment-url', {
      method: 'POST',
      body: JSON.stringify({
        payment_collection_id: paymentCollectionId,
        ...options
      })
    })
  }*/

  // Customer Digital Products
  async getCustomerDigitalProducts(token: string): Promise<ApiResponse<any[]>> {
    return this.request('/store/customers/me/digital-products', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
  }

}

export const apiClient = new ApiClient()
export default apiClient
