/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@500&family=Roboto:wght@300;400;500;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Hebrew Book Store - New Design Theme */
:root {
  /* New Color Palette */
  --color-navigation: #8D6E53;
  /* Navigation bar and dark text */
  --color-background: #F6F2E9;
  /* Background and light text */
  --color-accent: #62AEC8;
  /* Buttons and book titles */

  /* Primary Colors - Navigation/Dark Text */
  --color-primary: #8D6E53;
  --color-primary-light: #A08470;
  --color-primary-dark: #6B5440;

  /* Secondary Colors - Blue Accent */
  --color-secondary: #62AEC8;
  --color-secondary-light: #7BBDD4;
  --color-secondary-dark: #4A8BA3;

  /* Background Colors */
  --color-bg-primary: #F6F2E9;
  /* Main background */
  --color-bg-secondary: #FFFFFF;
  /* Card backgrounds */
  --color-bg-accent: #F0EDE6;
  /* Subtle accent background */
  --color-bg-subtle: #E8E3D8;
  /* Subtle warm background */

  /* Text Colors */
  --color-text-primary: #8D6E53;
  /* Dark text - navigation color */
  --color-text-secondary: #6B5440;
  /* Darker variant for secondary text */
  --color-text-accent: #62AEC8;
  /* Blue accent for buttons/titles */
  --color-text-light: #F6F2E9;
  /* Light text - background color */

  /* Border Colors */
  --color-border: #D4C5B9;
  /* Soft beige border */
  --color-border-light: #E8E0D6;
  /* Very light beige */
  --color-border-accent: #62AEC8;
  /* Blue accent border */

  /* Font Families */
  --font-title: 'Montserrat', sans-serif;
  --font-body: 'Roboto', sans-serif;
}

/* Typography Classes */
.font-title {
  font-family: var(--font-title);
  font-weight: 500;
  /* Montserrat Medium */
}

.font-body {
  font-family: var(--font-body);
  font-weight: 400;
  /* Roboto Regular */
}

.font-additional {
  font-family: var(--font-body);
  color: var(--color-accent);
}

.font-title-blue {
  font-family: var(--font-title);
  color: var(--color-text-accent);
  font-weight: 500;
  /* Montserrat Medium */
}

/* Decorative Elements */
.decorative-border {
  border: 1px solid var(--color-border);
  border-radius: 6px;
  position: relative;
}

.decorative-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 1px solid var(--color-border-light);
  border-radius: 8px;
  z-index: -1;
}

/* Button Styles */
.btn-primary {
  background: var(--color-background);
  color: var(--color-text-accent);
  border: 4px solid var(--color-text-primary);

  border-radius: 9999px;
  padding: 12px 24px;
  font-family: var(--font-body);
  font-weight: 500;
  letter-spacing: 0.3px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.btn-primary:hover {
  background: var(--color-secondary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.btn-secondary {
  background: var(--color-primary);
  color: var(--color-text-light);
  border: 1px solid var(--color-primary-dark);
  border-radius: 9999px;
  padding: 12px 24px;
  font-family: var(--font-body);
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: var(--color-primary-dark);
}

.btn-hero {
  background: var(--color-background);
  color: var(--color-text-primary);
  border-radius: 9999px;
  padding: 12px 24px;
  font-family: var(--font-body);
  font-weight: 500;
  letter-spacing: 0.3px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.btn-hero:hover {
  background: var(--color-primary-dark);
  color: var(--color-text-light);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

/* Card Styles */
.card-classic {
  background: var(--color-bg-primary);
  position: relative;
}

.card-book {
  display: flex;
  flex-direction: column;
  height: 100%;
  /* Ensures card fills grid cell */
  min-height: 420px;
  /* Set your desired min height */
  max-height: 480px;
  /* Optional: set a max height */
  background: var(--color-bg-primary);
  transition: box-shadow 0.2s, transform 0.2s;
}

.card-book .p-6 {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card-photo {
  background: rgb(239, 238, 238);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  max-width: 70%;
  padding: 2;
  margin: 2;
  border-bottom: 1px solid var(--color-border-light);
}

.input-base {
  width: 100%;
  font: var(--font-book-text);
  border-color: var(--color-border);
  background: var(--color-bg-primary);
  color: var(--color-text-primary); 
}

.header-div {
  background: var(--color-bg-primary);
  max-width: 80rem;
  padding-left: 1rem;
  padding-right: 1rem ;
  margin-left: auto;
  margin-right: auto;
  background: transparent;
}

.w-100 {
  width: 30rem;
}

@layer base {
  html {
    font-family: var(--font-body);
    background-color: var(--color-bg-primary);
    color: var(--color-text-primary);
    scroll-behavior: smooth;
  }

  body {
    font-family: var(--font-body);
    background-color: var(--color-bg-primary);
    color: var(--color-text-primary);
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: var(--font-title);
    font-weight: 500;
    color: var(--color-text-primary);
  }

  .book-title {
    color: var(--color-text-accent);
    font-family: var(--font-title);
    font-weight: 500;
  }

  .accent-text {
    color: var(--color-text-accent);
  }
}

/* Override browser autofill styling */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px var(--color-bg-primary) inset !important;
  -webkit-text-fill-color: var(--color-text-primary) !important;
  background-color: var(--color-bg-primary) !important;
  background-image: none !important;
  transition: background-color 5000s ease-in-out 0s;
}

/* For Firefox */
input:-moz-autofill {
  background-color: var(--color-bg-primary) !important;
  color: var(--color-text-primary) !important;
  box-shadow: 0 0 0 30px var(--color-bg-primary) inset !important;
}

/* For other browsers */
input:autofill {
  background-color: var(--color-bg-primary) !important;
  color: var(--color-text-primary) !important;
  box-shadow: 0 0 0 30px var(--color-bg-primary) inset !important;
}