<script lang="ts">
  import { onMount } from 'svelte'
  import { _ } from '$lib/i18n'
  import { apiClient } from '$lib/api/client'
  import { cartActions } from '$lib/stores/cart'
  
  export let cart: any
  export let onComplete: (result: { success: boolean, order?: any, error?: string }) => void
  export let onBack: () => void

  let stripeLoaded = false
  let stripe: any = null
  let checkout: any = null
  let paymentError = ''
  let checkoutSessionId = ''
  let checkoutInitialized = false

  // Get Stripe publishable key from environment
  const stripePublishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY

  onMount(async () => {
    await loadStripe()
    await createCheckoutSession()
  })

  // Reactive statement to initialize embedded checkout when ready
  $: if (stripeLoaded && checkoutSessionId && !checkoutInitialized && typeof document !== 'undefined') {
    const checkoutDiv = document.getElementById('checkout-element')
    if (checkoutDiv) {
      console.log('🔄 DOM element found, initializing Embedded Checkout')
      initializeEmbeddedCheckout()
      checkoutInitialized = true
    }
  }

  async function loadStripe() {
    try {
      // Dynamically import Stripe
      const { loadStripe } = await import('@stripe/stripe-js')
      
      if (!stripePublishableKey) {
        throw new Error('Stripe publishable key not found')
      }

      stripe = await loadStripe(stripePublishableKey)
      
      if (!stripe) {
        throw new Error('Failed to load Stripe')
      }

      stripeLoaded = true
      console.log('✅ Stripe loaded successfully')
    } catch (error) {
      console.error('Failed to load Stripe:', error)
      paymentError = $_('checkout.stripe_load_error')
    }
  }

  async function createCheckoutSession() {
    try {
      if (!cart?.id) {
        throw new Error('No cart found')
      }

      console.log('🔄 Using existing payment session from cart')
      console.log('🔍 Cart payment collection:', cart?.payment_collection)

      // Use the existing payment session client secret from MedusaJS
      const paymentSession = cart?.payment_collection?.payment_sessions?.[0]
      console.log('🔍 Payment session:', paymentSession)

      if (paymentSession?.data?.client_secret) {
        checkoutSessionId = paymentSession.data.client_secret
        console.log('✅ Using existing payment session client secret:', checkoutSessionId.substring(0, 20) + '...')
      } else {
        throw new Error('No payment session client secret found')
      }
    } catch (error) {
      console.error('❌ Failed to get payment session:', error)
      paymentError = $_('checkout.failed_to_initialize_payment')
    }
  }

  async function initializeEmbeddedCheckout() {
    if (!stripe || !checkoutSessionId) {
      console.warn('⚠️ Stripe not loaded or no checkout session ID')
      return
    }

    try {
      console.log('🔄 Initializing Stripe Embedded Checkout')

      // Initialize embedded checkout
      checkout = await stripe.initEmbeddedCheckout({
        clientSecret: checkoutSessionId
      })

      // Mount the checkout
      checkout.mount('#checkout-element')

      console.log('✅ Stripe Embedded Checkout initialized successfully')
    } catch (error) {
      console.error('❌ Failed to initialize Stripe Embedded Checkout:', error)
      paymentError = $_('checkout.stripe_load_error')
    }
  }

  // Handle checkout completion
  async function handleCheckoutComplete() {
    try {
      console.log('✅ Checkout completed, processing order')
      
      // Complete the cart
      const completeResponse = await apiClient.completeCart(cart.id)
      
      if (completeResponse.data?.type === 'order' && completeResponse.data.order) {
        // Order placed successfully
        console.log('✅ Order placed successfully:', completeResponse.data.order.id)
        cartActions.initialize() // Refresh cart
        onComplete({ 
          success: true, 
          order: completeResponse.data.order 
        })
      } else {
        console.error('❌ Cart completion failed')
        const errorMsg = $_('checkout.order_completion_failed')
        onComplete({ success: false, error: errorMsg })
      }
    } catch (error) {
      console.error('❌ Order completion error:', error)
      onComplete({ success: false, error: $_('checkout.payment_processing_error') })
    }
  }
</script>

<div class="space-y-6">
  <!-- Header -->
  <div class="text-center">
    <h3 class="text-xl font-bold mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-title);">
      {$_('checkout.payment_details')}
    </h3>
  </div>

  <!-- Error Display -->
  {#if paymentError}
    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
      <div class="flex items-center">
        <span class="text-red-500 mr-2">⚠️</span>
        <span class="text-red-700 font-book-text">{paymentError}</span>
      </div>
    </div>
  {/if}

  <!-- Loading State -->
  {#if !stripeLoaded || !checkoutSessionId}
    <div class="text-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
      <p class="text-sm" style="color: var(--color-text-secondary); font-family: var(--font-book-text);">
        {$_('checkout.loading_payment_system')}
      </p>
    </div>
  {/if}

  {#if stripeLoaded && checkoutSessionId}
    <!-- Stripe Embedded Checkout -->
    <div class="space-y-4">
      <div>
        <h4 class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
          {$_('checkout.payment_form')}
        </h4>
        <div class="border border-gray-300 rounded-lg bg-white">
          <div id="checkout-element">
            <!-- Stripe Embedded Checkout will render here -->
          </div>
        </div>
      </div>

      <!-- Security Notice -->
      <div class="flex items-center text-sm" style="color: var(--color-text-secondary);">
        <span class="text-green-500 mr-2">🔒</span>
        <span class="font-book-text">
          {$_('checkout.secure_payment_notice')}
        </span>
      </div>

      <!-- Back Button Only - Embedded Checkout handles payment -->
      <div class="flex justify-start pt-4">
        <button
          type="button"
          on:click={onBack}
          class="btn-classic-outline"
        >
          ← {$_('checkout.back')}
        </button>
      </div>
    </div>
  {/if}
</div>
