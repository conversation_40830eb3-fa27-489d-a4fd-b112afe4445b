import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { MedusaError } from "@medusajs/framework/utils"

// Handle Tranzila success callback
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const query = req.query as Record<string, string>
    const transactionId = query.myid
    const responseCode = query.Response
    const amount = query.sum
    const currency = query.currency
    const confirmationCode = query.ConfirmationCode
    const responseMessage = query.Responsemessage

    console.log('🔄 Tranzila callback received:', {
      transactionId,
      responseCode,
      amount,
      currency,
      confirmationCode,
      responseMessage
    })

    // Redirect to frontend with transaction details
    const frontendUrl = process.env.FRONTEND_URL || "http://localhost:5173"
    const params = new URLSearchParams({
      transaction_id: transactionId as string,
      response_code: responseCode as string,
      amount: amount as string,
      currency: currency as string,
      confirmation_code: confirmationCode as string || "",
      message: responseMessage as string || "",
    })

    if (responseCode === "000") {
      // Success - redirect to success page
      res.redirect(`${frontendUrl}/checkout/tranzila/success?${params.toString()}`)
    } else {
      // Error - redirect to error page
      res.redirect(`${frontendUrl}/checkout/tranzila/error?${params.toString()}`)
    }

  } catch (error) {
    console.error('❌ Error processing Tranzila callback:', error)
    
    // Redirect to error page
    const frontendUrl = process.env.FRONTEND_URL || "http://localhost:5173"
    res.redirect(`${frontendUrl}/checkout/tranzila/error?error=callback_processing_failed`)
  }
}

// Handle POST callbacks as well (some configurations might use POST)
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const responseData = req.body as Record<string, string>
    console.log('🔄 Tranzila POST callback received:', responseData)

    const transactionId = responseData.myid
    const responseCode = responseData.Response
    const amount = responseData.sum
    const currency = responseData.currency
    const confirmationCode = responseData.ConfirmationCode
    const responseMessage = responseData.Responsemessage

    // Redirect to frontend with transaction details
    const frontendUrl = process.env.FRONTEND_URL || "http://localhost:5173"
    const params = new URLSearchParams({
      transaction_id: transactionId || "",
      response_code: responseCode || "",
      amount: amount || "",
      currency: currency || "",
      confirmation_code: confirmationCode || "",
      message: responseMessage || "",
    })

    if (responseCode === "000") {
      // Success - redirect to success page
      res.redirect(`${frontendUrl}/checkout/tranzila/success?${params.toString()}`)
    } else {
      // Error - redirect to error page
      res.redirect(`${frontendUrl}/checkout/tranzila/error?${params.toString()}`)
    }

  } catch (error) {
    console.error('❌ Error processing Tranzila POST callback:', error)
    
    // Redirect to error page
    const frontendUrl = process.env.FRONTEND_URL || "http://localhost:5173"
    res.redirect(`${frontendUrl}/checkout/tranzila/error?error=callback_processing_failed`)
  }
}
