<script lang="ts">
  import { createEventDispatcher } from 'svelte'
  import { _ } from '$lib/i18n'
  import RegistrationForm from './RegistrationForm.svelte'
  import LoginForm from './LoginForm.svelte'

  export let isOpen = false
  export let mode: 'login' | 'register' = 'login'

  const dispatch = createEventDispatcher()

  function closeModal() {
    isOpen = false
    dispatch('close')
  }

  function handleSuccess(event: CustomEvent) {
    dispatch('success', event.detail)
    closeModal()
  }

  function switchToLogin() {
    mode = 'login'
  }

  function switchToRegister() {
    mode = 'register'
  }

  // Close modal on escape key
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      closeModal()
    }
  }

</script>

<svelte:window on:keydown={handleKeydown} />

{#if isOpen}
  <!-- Modal Backdrop -->
  <div
    class="fixed inset-0 z-50 flex items-center justify-center p-4"
    style="background: rgba(0, 0, 0, 0.5);"
    role="dialog"
    aria-modal="true"
    aria-labelledby="auth-modal-title"
  >
    <!-- Modal Content -->
    <div
      class="relative w-full max-w-md max-h-[90vh] overflow-y-auto rounded-lg shadow-xl"
      style="background: var(--color-bg-primary); border: 1px solid var(--color-border);"
      on:click|stopPropagation
    >
      <!-- Close Button -->
      <button
        on:click={closeModal}
        class="absolute top-4 right-4 p-2 rounded-full hover:bg-opacity-10 transition-colors"
        style="color: var(--color-text-secondary); hover:background: var(--color-text-secondary);"
        aria-label={$_('common.close')}
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>

      <!-- Modal Header -->
      <div class="p-6 pb-4">
        <h2 id="auth-modal-title" class="font-book-title text-2xl font-bold text-center" style="color: var(--color-text-primary);">
          {mode === 'login' ? $_('auth.welcome_back') : $_('auth.create_account')}
        </h2>
      </div>

      <!-- Modal Body -->
      <div class="px-6 pb-6">
        {#if mode === 'login'}
          <LoginForm
            on:success={handleSuccess}
            on:switch-to-register={switchToRegister}
          />
        {:else}
          <RegistrationForm
            on:success={handleSuccess}
            on:switch-to-login={switchToLogin}
          />
        {/if}
      </div>
    </div>
  </div>
{/if}

<style>
  /* Ensure modal appears above everything */
  :global(.auth-modal-open) {
    overflow: hidden;
  }
</style>
