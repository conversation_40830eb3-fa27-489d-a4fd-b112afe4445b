import {
  IPaymentProvider,
} from "@medusajs/framework/types"
import { MedusaError } from "@medusajs/framework/utils"
import crypto from "crypto"

export interface AllPayOptions {
  login: string
  api_key: string
  currency?: string
  language?: string
  test_mode?: boolean
  iframe_mode?: boolean
}

// AllPay API interfaces
export interface AllPayItem {
  name: string
  qty: number
  price: number
  vat: number
}

export interface AllPayPaymentRequest {
  login: string
  order_id: string
  items: AllPayItem[]
  currency?: string
  lang?: string
  notifications_url?: string
  success_url?: string
  backlink_url?: string
  client_name?: string
  client_email?: string
  client_phone?: string
  client_tehudat?: string
  add_field_1?: string
  add_field_2?: string
  expire?: number
  sign: string
}

export interface AllPayPaymentResponse {
  payment_url: string
  order_id: string
  status?: string
  message?: string
}

export interface AllPayNotificationData {
  order_id: string
  amount: number
  status: number
  client_name?: string
  client_email?: string
  client_tehudat?: string
  foreign_card: number
  card_mask?: string
  card_brand?: string
  currency: string
  receipt?: string
  add_field_1?: string
  add_field_2?: string
  sign: string
}

export interface AllPayStatusResponse {
  order_id: string
  status: number
  amount: number
  currency: string
  card_mask?: string
  card_brand?: string
  foreign_card: number
  receipt?: string
}

class AllPayPaymentProviderService implements IPaymentProvider {
  static identifier = "allpay"

  protected options_: AllPayOptions
  private readonly apiUrl = "https://allpay.to/app/"

  constructor(container: any, options: AllPayOptions) {
    this.options_ = {
      currency: "ILS",
      language: "AUTO",
      test_mode: false,
      iframe_mode: true,
      ...options,
    }

    if (!this.options_.login) {
      throw new MedusaError(
        MedusaError.Types.INVALID_ARGUMENT,
        "AllPay login is required"
      )
    }

    if (!this.options_.api_key) {
      throw new MedusaError(
        MedusaError.Types.INVALID_ARGUMENT,
        "AllPay API key is required"
      )
    }
  }

  /**
   * Generate SHA256 signature for AllPay API requests
   */
  private generateSignature(params: Record<string, any>): string {
    // Remove sign parameter if it exists
    const { sign, ...paramsWithoutSign } = params

    // Sort keys alphabetically
    const sortedKeys = Object.keys(paramsWithoutSign).sort()
    const chunks: string[] = []

    sortedKeys.forEach((key) => {
      const value = paramsWithoutSign[key]

      if (Array.isArray(value)) {
        value.forEach((item) => {
          if (typeof item === 'object' && item !== null) {
            const sortedItemKeys = Object.keys(item).sort()
            sortedItemKeys.forEach((itemKey) => {
              const itemValue = item[itemKey]
              if (itemValue !== null && itemValue !== undefined && String(itemValue).trim() !== '') {
                chunks.push(String(itemValue).trim())
              }
            })
          }
        })
      } else {
        if (value !== null && value !== undefined && String(value).trim() !== '') {
          chunks.push(String(value).trim())
        }
      }
    })

    // Create signature string
    const signatureString = chunks.join(':') + ':' + this.options_.api_key
    
    // Generate SHA256 hash
    return crypto.createHash('sha256').update(signatureString).digest('hex')
  }

  /**
   * Create payment request to AllPay
   */
  async createPaymentRequest(request: Omit<AllPayPaymentRequest, 'sign'>): Promise<AllPayPaymentResponse> {
    try {
      const requestWithSign = {
        ...request,
        sign: this.generateSignature(request)
      }

      console.log('🔄 Creating AllPay payment request:', {
        order_id: request.order_id,
        amount: request.items.reduce((sum, item) => sum + (item.price * item.qty), 0),
        currency: request.currency
      })

      const response = await fetch(`${this.apiUrl}?show=getpayment&mode=api8`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestWithSign),
      })

      if (!response.ok) {
        throw new Error(`AllPay API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()

      if (!data.payment_url) {
        throw new Error(`AllPay API error: ${data.message || 'No payment URL returned'}`)
      }

      console.log('✅ AllPay payment request created successfully:', data.payment_url)
      return data
    } catch (error) {
      console.error('❌ AllPay payment request failed:', error)
      throw new MedusaError(
        MedusaError.Types.PAYMENT_AUTHORIZATION_ERROR,
        `Failed to create AllPay payment: ${error.message}`
      )
    }
  }

  /**
   * Verify payment status with AllPay
   */
  async verifyPaymentStatus(orderId: string): Promise<AllPayStatusResponse> {
    try {
      const request = {
        login: this.options_.login,
        order_id: orderId,
      }

      const requestWithSign = {
        ...request,
        sign: this.generateSignature(request)
      }

      const response = await fetch(`${this.apiUrl}?show=paymentstatus&mode=api8`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestWithSign),
      })

      if (!response.ok) {
        throw new Error(`AllPay status API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('❌ AllPay status verification failed:', error)
      throw new MedusaError(
        MedusaError.Types.PAYMENT_AUTHORIZATION_ERROR,
        `Failed to verify AllPay payment status: ${error.message}`
      )
    }
  }

  /**
   * Verify webhook signature
   */
  verifyWebhookSignature(data: AllPayNotificationData): boolean {
    try {
      const expectedSignature = this.generateSignature(data)
      return expectedSignature === data.sign
    } catch (error) {
      console.error('❌ AllPay webhook signature verification failed:', error)
      return false
    }
  }

  private buildError(message: string, error?: any): any {
    return {
      error: message,
      code: error?.code || "ALLPAY_ERROR",
      detail: error?.message || error,
    }
  }

  private buildSuccess(data: any): any {
    return {
      session_data: data,
    }
  }

  // IPaymentProvider interface methods

  getIdentifier(): string {
    return "allpay"
  }

  async getWebhookActionAndData(data: any): Promise<any> {
    return {
      action: "payment_update",
      data: data
    }
  }

  async retrievePayment(
    paymentSessionData: Record<string, unknown>
  ): Promise<any> {
    try {
      return {
        session_data: paymentSessionData,
      }
    } catch (error) {
      return this.buildError("Failed to retrieve AllPay payment", error)
    }
  }

  async initiatePayment(context: any): Promise<any> {
    console.log('🔄 Initiating AllPay payment:', context)
    try {
      const { amount, currency_code, context: paymentContext, resource_id } = context

      console.log('🔄 Initiating AllPay payment with context:', { amount, currency_code, resource_id })

      const orderId = resource_id || `order_${Date.now()}`
      const transactionId = `${orderId}_${Date.now()}`

      // Calculate total amount from cents to currency units
      const totalAmount = Number((amount / 100).toFixed(2))

      // Prepare payment request
      const paymentRequest: Omit<AllPayPaymentRequest, 'sign'> = {
        login: this.options_.login,
        order_id: transactionId,
        items: [
          {
            name: "Order Payment",
            qty: 1,
            price: totalAmount,
            vat: 1, // 18% VAT included
          }
        ],
        currency: currency_code?.toUpperCase() || this.options_.currency || "ILS",
        lang: this.options_.language || "AUTO",
        notifications_url: `${process.env.MEDUSA_BACKEND_URL || "http://localhost:9000"}/webhooks/allpay`,
        success_url: `${process.env.FRONTEND_URL || "http://localhost:5173"}/checkout/allpay/success`,
        backlink_url: `${process.env.FRONTEND_URL || "http://localhost:5173"}/checkout`,
        expire: Math.floor(Date.now() / 1000) + 3600, // 1 hour expiration
      }

      // Create payment with AllPay
      const paymentResponse = await this.createPaymentRequest(paymentRequest)

      return this.buildSuccess({
        id: transactionId,
        status: "pending",
        amount: amount,
        currency_code: currency_code,
        payment_url: paymentResponse.payment_url,
        order_id: transactionId,
        provider_id: "allpay",
        data: {
          payment_url: paymentResponse.payment_url,
          order_id: transactionId,
          amount: totalAmount,
          currency: currency_code,
        },
      })
    } catch (error) {
      console.error('❌ AllPay payment initiation failed:', error)
      return this.buildError("Failed to initiate AllPay payment", error)
    }
  }

  async authorizePayment(data: any): Promise<any> {
    try {
      console.log('🔄 Authorizing AllPay payment:', data)

      // For AllPay, authorization happens on their side
      // We just need to verify the payment was successful
      const { payment_data } = data as any

      return this.buildSuccess({
        ...data,
        status: "authorized",
        authorized_at: new Date().toISOString(),
      })
    } catch (error) {
      console.error('❌ AllPay payment authorization failed:', error)
      return this.buildError("Failed to authorize AllPay payment", error)
    }
  }

  async capturePayment(paymentSessionData: Record<string, unknown>): Promise<any> {
    try {
      console.log('🔄 Capturing AllPay payment:', paymentSessionData)

      // For AllPay, capture happens automatically after authorization
      // We just need to mark the payment as captured

      return this.buildSuccess({
        ...paymentSessionData,
        status: "captured",
        captured_at: new Date().toISOString(),
      })
    } catch (error) {
      console.error('❌ AllPay payment capture failed:', error)
      return this.buildError("Failed to capture AllPay payment", error)
    }
  }

  async refundPayment(data: any): Promise<any> {
    try {
      console.log('🔄 Refunding AllPay payment:', data)

      const orderId = data.order_id as string
      const refundAmount = data.amount || 0

      if (!orderId) {
        throw new Error("Order ID is required for refund")
      }

      // Calculate refund amount in currency units
      const refundAmountInCurrency = Number((refundAmount / 100).toFixed(2))

      // Prepare refund request
      const refundRequest = {
        login: this.options_.login,
        order_id: orderId,
        amount: refundAmountInCurrency,
      }

      const requestWithSign = {
        ...refundRequest,
        sign: this.generateSignature(refundRequest)
      }

      const response = await fetch(`${this.apiUrl}?show=refund&mode=api8`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestWithSign),
      })

      if (!response.ok) {
        throw new Error(`AllPay refund API error: ${response.status} ${response.statusText}`)
      }

      const responseData = await response.json()

      if (responseData.status !== 3 && responseData.status !== 4) { // 3 = refunded, 4 = partially refunded
        throw new Error(`AllPay refund failed: ${responseData.message || 'Unknown error'}`)
      }

      console.log('✅ AllPay refund successful:', responseData)

      return this.buildSuccess({
        ...data,
        status: responseData.status === 3 ? "refunded" : "partially_refunded",
        refunded_at: new Date().toISOString(),
        refund_amount: refundAmount,
      })
    } catch (error) {
      console.error('❌ AllPay refund failed:', error)
      return this.buildError("Failed to refund AllPay payment", error)
    }
  }

  async cancelPayment(paymentSessionData: Record<string, unknown>): Promise<any> {
    try {
      console.log('🔄 Cancelling AllPay payment:', paymentSessionData)

      // AllPay doesn't have a specific cancel endpoint
      // Payments expire automatically based on the expire parameter

      return this.buildSuccess({
        ...paymentSessionData,
        status: "canceled",
        canceled_at: new Date().toISOString(),
      })
    } catch (error) {
      console.error('❌ AllPay payment cancellation failed:', error)
      return this.buildError("Failed to cancel AllPay payment", error)
    }
  }

  async getPaymentStatus(data: any): Promise<any> {
    try {
      const orderId = data.order_id as string
      if (!orderId) {
        return { status: "error" }
      }

      const statusResponse = await this.verifyPaymentStatus(orderId)

      // Map AllPay status to readable status
      switch (statusResponse.status) {
        case 0: // unpaid (pending or failed)
          return { status: "pending" }
        case 1: // successful payment
          return { status: "authorized" }
        case 3: // refunded
          return { status: "canceled" }
        case 4: // partially refunded
          return { status: "authorized" }
        default:
          return { status: "error" }
      }
    } catch (error) {
      console.error('❌ AllPay status check failed:', error)
      return { status: "error" }
    }
  }

  async deletePayment(paymentSessionData: Record<string, unknown>): Promise<any> {
    try {
      console.log('🔄 Deleting AllPay payment session:', paymentSessionData)

      // AllPay doesn't require explicit deletion
      // Payment sessions expire automatically

      return this.buildSuccess({
        ...paymentSessionData,
        deleted_at: new Date().toISOString(),
      })
    } catch (error) {
      console.error('❌ AllPay payment deletion failed:', error)
      return this.buildError("Failed to delete AllPay payment", error)
    }
  }

  async updatePayment(context: any): Promise<any> {
    try {
      console.log('🔄 Updating AllPay payment:', context)

      // For AllPay, we need to create a new payment session for updates
      // as the original payment URL cannot be modified
      return await this.initiatePayment(context)
    } catch (error) {
      console.error('❌ AllPay payment update failed:', error)
      return this.buildError("Failed to update AllPay payment", error)
    }
  }
}

export default AllPayPaymentProviderService
