<script lang="ts">
  import { onMount } from 'svelte'
  import { _ } from '$lib/i18n'
  import { apiClient } from '$lib/api/client'
  import { cartActions } from '$lib/stores/cart'
  import { formatPriceWithCurrency } from '$lib/stores/currency'

  export let cart: any
  export let onComplete: (result: { success: boolean, order?: any, error?: string }) => void
  export let onBack: () => void

  let stripeLoaded = false
  let stripe: any = null
  let elements: any = null
  let cardElement: any = null
  let isProcessing = false
  let paymentError = ''
  let clientSecret = ''
  let elementsInitialized = false

  // Get Stripe publishable key from environment
  const stripePublishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY

  onMount(async () => {
    await loadStripe()
    extractClientSecret()
  })

  // Reactive statement to initialize elements when everything is ready
  $: if (stripeLoaded && clientSecret && !elementsInitialized && typeof document !== 'undefined') {
    // Small delay to ensure DOM is ready
    setTimeout(() => {
      const cardElementDiv = document.getElementById('card-element')
      if (cardElementDiv && !elementsInitialized) {
        console.log('🔄 DOM element found, initializing Stripe Elements')
        initializeElements()
        elementsInitialized = true
      }
    }, 100)
  }

  async function loadStripe() {
    try {
      const { loadStripe } = await import('@stripe/stripe-js')
      
      if (!stripePublishableKey) {
        throw new Error('Stripe publishable key not found')
      }

      stripe = await loadStripe(stripePublishableKey)
      
      if (!stripe) {
        throw new Error('Failed to load Stripe')
      }

      stripeLoaded = true
      console.log('✅ Stripe loaded successfully')
    } catch (error) {
      console.error('Failed to load Stripe:', error)
      paymentError = $_('checkout.stripe_load_error')
    }
  }

  function extractClientSecret() {
    console.log('🔍 Extracting client secret from cart:', cart)
    console.log('🔍 Payment collection:', cart?.payment_collection)
    
    const paymentSession = cart?.payment_collection?.payment_sessions?.[0]
    console.log('🔍 Payment session:', paymentSession)
    
    if (paymentSession?.data?.client_secret) {
      clientSecret = paymentSession.data.client_secret
      console.log('✅ Client secret found:', clientSecret.substring(0, 20) + '...')
    } else {
      console.warn('⚠️ No client secret found in payment session')
      paymentError = $_('checkout.no_client_secret')
    }
  }

  function initializeElements() {
    if (!stripe || !clientSecret) {
      console.warn('⚠️ Stripe or client secret not ready')
      return
    }

    const cardElementDiv = document.getElementById('card-element')
    if (!cardElementDiv) {
      console.warn('⚠️ Card element not found in DOM')
      return
    }

    console.log('🔄 Initializing Stripe Elements')

    try {
      // Create elements without clientSecret for basic card element
      elements = stripe.elements({
        appearance: {
          theme: 'stripe',
          variables: {
            colorPrimary: '#3b82f6',
            colorBackground: '#ffffff',
            colorText: '#1f2937',
            colorDanger: '#ef4444',
            fontFamily: 'system-ui, sans-serif',
            spacingUnit: '4px',
            borderRadius: '8px'
          }
        }
      })

      // Create basic card element
      cardElement = elements.create('card', {
        style: {
          base: {
            fontSize: '16px',
            color: '#1f2937',
            '::placeholder': {
              color: '#9ca3af'
            }
          }
        }
      })

      // Mount card element
      cardElement.mount('#card-element')

      // Listen for changes
      cardElement.on('change', (event: any) => {
        if (event.error) {
          paymentError = event.error.message
        } else {
          paymentError = ''
        }
      })

      console.log('✅ Stripe Elements initialized successfully')
    } catch (error) {
      console.error('❌ Failed to initialize Stripe Elements:', error)
      paymentError = $_('checkout.stripe_load_error')
    }
  }

  async function handlePayment() {
    if (!stripe || !cardElement || !cart || !clientSecret) {
      paymentError = $_('checkout.payment_not_ready')
      return
    }

    console.log('🔍 Cart data for payment:', cart)

    try {
      isProcessing = true
      paymentError = ''

      console.log('🔄 Processing payment with Stripe')

      // Confirm card payment using the client secret from MedusaJS
      const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: cardElement,
          billing_details: {
            name: `${cart.billing_address?.first_name || ''} ${cart.billing_address?.last_name || ''}`.trim(),
            email: cart.email,
            phone: cart.billing_address?.phone,
            address: {
              city: cart.billing_address?.city,
              country: cart.billing_address?.country_code?.toLowerCase(),
              line1: cart.billing_address?.address_1,
              line2: cart.billing_address?.address_2,
              postal_code: cart.billing_address?.postal_code,
            },
          },
        },
      })

      if (error) {
        console.error('❌ Stripe payment error:', error)
        paymentError = error.message || $_('checkout.payment_failed')
        onComplete({ success: false, error: paymentError })
        return
      }

      if (paymentIntent.status === 'succeeded' || paymentIntent.status === 'requires_capture') {
        console.log('✅ Payment processed:', paymentIntent.status)

        // Complete the cart - MedusaJS will handle capture if needed
        console.log('🔄 Completing cart:', cart.id)
        const completeResponse = await apiClient.completeCart(cart.id)

        console.log('🔍 Cart completion response:', completeResponse)

        if (completeResponse.data?.type === 'order' && completeResponse.data.order) {
          console.log('✅ Order placed successfully:', completeResponse.data.order.id)

          // Post-payment cleanup and cart management
          await handlePostPaymentCleanup(completeResponse.data.order)

          // Remove cart ID from localStorage as per MedusaJS docs
          localStorage.removeItem('cart_id')
          console.log('✅ Cart ID removed from localStorage')

          // Redirect to success page
          console.log('🔄 Redirecting to success page')
          window.location.href = `/checkout/success`
        } else if (completeResponse.data?.type === 'cart') {
          // Cart completion failed - this is the expected error format from MedusaJS
          console.error('❌ Cart completion failed:', completeResponse.data.error)
          const errorMsg = completeResponse.data?.error || $_('checkout.order_completion_failed')
          paymentError = errorMsg
          onComplete({ success: false, error: errorMsg })
        } else {
          console.error('❌ Unexpected cart completion response:', completeResponse)
          paymentError = $_('checkout.order_completion_failed')
          onComplete({ success: false, error: paymentError })
        }
      } else {
        console.warn('⚠️ Payment not completed:', paymentIntent.status)
        paymentError = $_('checkout.payment_incomplete')
        onComplete({ success: false, error: paymentError })
      }
    } catch (error) {
      console.error('❌ Payment processing error:', error)
      paymentError = $_('checkout.payment_processing_error')
      onComplete({ success: false, error: paymentError })
    } finally {
      isProcessing = false
    }
  }

  async function handlePostPaymentCleanup(order: any) {
    try {
      console.log('🔄 Starting post-payment cleanup for order:', order.id)

      // Get auth token from localStorage
      const token = localStorage.getItem('auth_token')
      if (!token) {
        console.warn('⚠️ No auth token found for post-payment cleanup')
        return
      }

      // Get current customer
      const customer = await apiClient.getCustomer(token)
      if (!customer.data?.customer) {
        console.warn('⚠️ No customer found for post-payment cleanup')
        return
      }

      const customerId = customer.data.customer.id
      console.log('🔄 Processing cleanup for customer:', customerId)

      // Step 1: Remove old cart ID from customer metadata
      const currentMetadata = customer.data.customer.metadata || {}
      delete currentMetadata.cart_id

      // Step 2: Create a new cart for the customer
      console.log('🔄 Creating new cart for customer')
      const newCartResponse = await apiClient.createCart({
        region_id: cart.region_id || cart.region?.id
      })

      if (newCartResponse.data?.cart) {
        const newCartId = newCartResponse.data.cart.id
        console.log('✅ New cart created:', newCartId)

        // Step 3: Update customer metadata with new cart ID
        await apiClient.updateCustomer(customerId, {
          metadata: {
            ...currentMetadata,
            cart_id: newCartId
          }
        }, token)
        console.log('✅ Customer metadata updated with new cart ID')

        // Step 4: Don't initialize cart store immediately to avoid redirect issues
        // The success page or next page load will handle cart initialization
        console.log('✅ Post-payment cleanup completed - cart will be reinitialized on next page load')
      } else {
        console.error('❌ Failed to create new cart')
      }
    } catch (error) {
      console.error('❌ Post-payment cleanup failed:', error)
      // Don't throw error - order was successful, cleanup is secondary
    }
  }
</script>

<div class="space-y-6">
  <!-- Header -->
  <div class="text-center">
    <h3 class="text-xl font-bold mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-title);">
      {$_('checkout.payment_details')}
    </h3>
  </div>

  <!-- Error Display -->
  {#if paymentError}
    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
      <div class="flex items-center">
        <span class="text-red-500 mr-2">⚠️</span>
        <span class="text-red-700 font-book-text">{paymentError}</span>
      </div>
    </div>
  {/if}

  <!-- Loading State -->
  {#if !stripeLoaded}
    <div class="text-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
      <p class="text-sm" style="color: var(--color-text-secondary); font-family: var(--font-book-text);">
        {$_('checkout.loading_payment_system')}
      </p>
    </div>
  {/if}

  {#if stripeLoaded && clientSecret}
    <!-- Payment Form -->
    <div class="space-y-4">
      <div>
        <label for="card-element" class="block text-sm font-medium mb-2" style="color: var(--color-text-primary); font-family: var(--font-book-text);">
          {$_('checkout.card_details')} *
        </label>
        <div class="border border-gray-300 rounded-lg p-4 bg-white">
          <div id="card-element">
            <!-- Stripe Elements will create form elements here -->
          </div>
        </div>
      </div>

      <!-- Security Notice -->
      <div class="flex items-center text-sm" style="color: var(--color-text-secondary);">
        <span class="text-green-500 mr-2">🔒</span>
        <span class="font-book-text">
          {$_('checkout.secure_payment_notice')}
        </span>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-between pt-4">
        <button
          type="button"
          on:click={onBack}
          disabled={isProcessing}
          class="btn-classic-outline"
          class:opacity-50={isProcessing}
        >
          ← {$_('checkout.back')}
        </button>

        <button
          type="button"
          on:click={handlePayment}
          disabled={isProcessing || !stripeLoaded || !!paymentError || !elementsInitialized}
          class="btn-primary"
          class:opacity-50={isProcessing || !stripeLoaded || !!paymentError || !elementsInitialized}
        >
          {#if isProcessing}
            <div class="flex items-center">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              {$_('checkout.processing_payment')}
            </div>
          {:else}
            {$_('checkout.place_order')} - {cart.total ? formatPriceWithCurrency(cart.total) : cart.total_amount ? 
              formatPriceWithCurrency(cart.total_amount) : '0.00'}
          {/if}
        </button>
      </div>
    </div>
  {/if}
</div>
