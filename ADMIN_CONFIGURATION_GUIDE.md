# 📋 Admin Configuration Guide
## How to Configure Your Main Store Page

Your Hebrew Book Store now has the foundation for dynamic configuration of the main page content through the MedusaJS admin panel. Here's the current status and how to use it:

## 🚀 **Quick Start: Configure Video**

**Want to change the main page video right now?**

1. Go to `http://localhost:9000/app` (admin panel)
2. Login → Navigate to **Settings** → **Store**
3. Scroll down to **"Metadata"** section
4. Click the edit icon (⚙️) next to "Metadata"
5. Add: Key = `main_video_url`, Value = `https://www.youtube.com/embed/YOUR_VIDEO_ID`
6. Click **"Save"**
7. Visit `http://localhost:5173` to see your new video!

---

## ✅ **What's Currently Working**

### Backend Infrastructure:
- ✅ **Settings Module**: Complete MedusaJS module for managing store settings
- ✅ **Database Model**: `store_setting` table for persistent settings storage
- ✅ **API Endpoints**: RESTful endpoints for reading/writing settings
- ✅ **Admin Page**: Settings management page in MedusaJS admin panel
- ✅ **Frontend Integration**: Main page fetches settings from API

### Current Status:
- ✅ Backend is running successfully
- ✅ Settings module is loaded and functional
- ✅ Admin page is available at `/app/settings` in the admin panel
- ⚠️ Store API requires publishable key (needs configuration)
- ⚠️ Database migration needed for settings table

---

## 🎬 **Video Configuration (Ready to Use)**

### What You Can Configure:
- **Main page video URL** (YouTube embed link)
- **Video title** (displayed when no video is set)
- **Store name and description**

### How to Configure Video:

#### **Step 1: Access Admin Panel**
1. Open your browser and go to `http://localhost:9000/app`
2. Login with your admin credentials

#### **Step 2: Navigate to Store Settings**
1. In the admin panel sidebar, click **"Settings"**
2. Click **"Store"** from the settings menu
3. Scroll down to find the **"Metadata"** section

#### **Step 3: Configure Video via Store Metadata**
1. **Get Your YouTube Video URL**:
   - Go to your YouTube video
   - Click **"Share"** → **"Embed"**
   - Copy the URL from the `src` attribute (e.g., `https://www.youtube.com/embed/YOUR_VIDEO_ID`)

2. **Add Video URLs to Metadata**:
   - Click the edit icon (⚙️) next to **"Metadata"**
   - In the metadata editor, add both:
     - **Key**: `main_video_url` (for about section)
     - **Value**: `https://www.youtube.com/embed/YOUR_VIDEO_ID`
     - **Key**: `hero_video_url` (for full-screen hero section)
     - **Value**: `https://www.youtube.com/embed/YOUR_HERO_VIDEO_ID`
   - ✅ **Example**:
     ```json
     {
       "main_video_url": "https://www.youtube.com/embed/dQw4w9WgXcQ",
       "hero_video_url": "https://www.youtube.com/embed/HERO_VIDEO_ID"
     }
     ```
   - ❌ **Don't use**: `https://www.youtube.com/watch?v=dQw4w9WgXcQ`

3. **Save Changes**:
   - Click **"Save"** to apply the new video URLs
   - The hero video will appear at the top of your store's main page
   - The main video will appear in the about section

#### **Step 4: Verify Changes**
1. Open your store in a new tab: `http://localhost:5173`
2. Check that your new video appears on the homepage
3. If you don't see changes, refresh the page (`Ctrl+F5`)

4. **Result**
   - Video appears immediately on the main page (after refresh)
   - If no video URL is set, shows a placeholder with your title

### 📝 **Important Notes**

#### **⚠️ CRITICAL: Use Embed URLs Only**
**❌ WRONG**: `https://www.youtube.com/watch?v=VIDEO_ID` (causes "X-Frame-Options" error)
**✅ CORRECT**: `https://www.youtube.com/embed/VIDEO_ID` (works in iframe)

#### **Supported Video Formats**:
- ✅ **YouTube**: `https://www.youtube.com/embed/VIDEO_ID`
- ✅ **Vimeo**: `https://player.vimeo.com/video/VIDEO_ID`
- ❌ **Direct video files** (MP4, etc.) are not supported yet
- ❌ **Regular YouTube/Vimeo URLs** will cause frame errors

#### **Video Requirements**:
- Use **embed URLs** only (not regular YouTube/Vimeo links)
- Video should be **public** or **unlisted** (not private)
- Recommended aspect ratio: **16:9** for best display

#### **Getting the Right URL**:
**For YouTube**:
1. Go to your video → Click "Share" → Click "Embed"
2. Copy the URL from `src="..."` in the embed code
3. Example: `https://www.youtube.com/embed/dQw4w9WgXcQ`

**For Vimeo**:
1. Go to your video → Click "Share" → Click "Embed"
2. Copy the URL from `src="..."` in the embed code
3. Example: `https://player.vimeo.com/video/123456789`

### 🔧 **Troubleshooting**

#### **❌ "Refused to display in a frame" Error**:
**Error**: `Refused to display 'https://www.youtube.com/' in a frame because it set 'X-Frame-Options' to 'sameorigin'`

**Cause**: You're using a regular YouTube URL instead of an embed URL.

**Solution**:
1. ❌ **Wrong**: `https://www.youtube.com/watch?v=VIDEO_ID`
2. ✅ **Correct**: `https://www.youtube.com/embed/VIDEO_ID`
3. **How to get embed URL**:
   - Go to your YouTube video
   - Click **"Share"** → **"Embed"**
   - Copy the URL from `src="..."` in the embed code
4. **Update store metadata** with the correct embed URL

#### **Video Not Showing**:
1. Check that you used an **embed URL** (not a regular video URL)
2. Verify the video is **public** or **unlisted**
3. Try refreshing the store page (`Ctrl+F5`)
4. Check browser console for errors

#### **Settings Not Saving**:
1. Make sure you're logged into the admin panel
2. Check that the backend server is running (`http://localhost:9000`)
3. Verify you have admin permissions

#### **Store Page Shows Default Video**:
1. **Check store metadata**:
   - Go to admin panel → Settings → Store → Metadata
   - Verify `main_video_url` key exists with correct embed URL
2. **Check browser network tab** for API errors
3. **Verify backend server** is running (`http://localhost:9000`)
4. **Clear browser cache** and refresh the page

### 🔑 **Fix CORS/API Key Issues**

If you see CORS errors in the browser console, follow these steps:

#### **Step 1: Get the Correct Publishable API Key**
1. Go to admin panel: `http://localhost:9000/app`
2. Navigate to: **Settings** → **Developer Settings** → **Publishable API Keys**
3. Click on an existing key or create a new one
4. **Copy the full API key** (click on the truncated key to copy)
5. **Ensure it has a sales channel** associated with it

#### **Step 2: Update Frontend Code**
1. Open `frontend/src/routes/+page.svelte`
2. Find the line with `'x-publishable-api-key': 'pk_...'`
3. Replace the key with your copied key
4. Save the file

#### **Step 3: Verify CORS Configuration**
1. Check `backend/.env` file
2. Ensure: `STORE_CORS=http://localhost:5173,http://localhost:3000`
3. Restart backend server: `cd backend && yarn dev`

#### **Step 4: Test the Connection**
1. Open browser console (`F12`)
2. Refresh your store page (`http://localhost:5173`)
3. Check for any remaining CORS or API errors

---

## 📚 **Book Products Configuration**

### What You Can Configure:
- **Book titles and descriptions**
- **Book pricing**
- **Book images/thumbnails**
- **Book metadata** (difficulty level, duration, etc.)
- **Chapter information** (table of contents)

### How to Configure:

1. **Access Products Section**
   - In the admin panel, go to **"Products"** in the sidebar
   - This is where you manage all your book products

2. **Create/Edit a Book Product**
   - Click **"Add Product"** for new books
   - Or click on existing book to edit

3. **Basic Book Information**
   - **Title**: Book name (e.g., "Hebrew for Beginners")
   - **Description**: Detailed book description
   - **Handle**: URL-friendly name (auto-generated)
   - **Thumbnail**: Upload book cover image

4. **Book Pricing**
   - Go to **"Pricing"** tab
   - Set price in your currency
   - Configure different regional pricing if needed

5. **Book Metadata** (Custom Fields)
   - **Difficulty Level**: beginner/intermediate/advanced
   - **Estimated Duration**: e.g., "6 weeks"
   - **Total Chapters**: Number of chapters
   - **Features**: What students will learn
   - **Methodology**: Teaching approach description

6. **Chapter Management**
   - Chapters are managed separately in your custom chapter system
   - Each chapter links to a book via `book_id`
   - Configure chapter order, content, and pricing individually

---

## 🎯 **Main Page Content Structure**

Your main page automatically displays:

### 1. **Video Section** (Top)
- Configured through Settings widget
- Shows video if URL is provided
- Shows placeholder if no video

### 2. **Books Section** (Middle)
- Automatically pulls from Products in admin
- Shows book tabs for navigation
- Displays selected book details:
  - Cover image (from product thumbnail)
  - Title and subtitle
  - Price and duration
  - Description
  - Features list
  - Methodology
  - Chapter count

### 3. **Chapters Section** (Bottom)
- Shows table of contents for selected book
- Interactive chapter previews
- Free vs paid chapter indicators
- Chapter difficulty levels

---

## ⚙️ **Store Settings Available**

Through the Settings widget, you can configure:

### Homepage Settings:
- **Main Page Video URL**: YouTube embed link
- **Main Page Video Title**: Fallback title when no video

### General Settings:
- **Store Name**: Your store's name
- **Store Description**: Brief store description

---

## 🔧 **Current Status & Next Steps**

### ✅ **What's Working Now:**
- Backend server is running successfully
- Settings module is loaded and functional
- Admin settings page exists at `/app/settings`
- Frontend is ready to display dynamic content
- API endpoints are working

### ⚠️ **Known Issues:**
- Build command has TypeScript errors (from existing code, not our settings module)
- Database migration needs to be run to create the settings table
- Store API requires publishable key configuration

### 🛠️ **To Complete Setup:**

#### Option 1: Fix TypeScript Errors First
1. Fix the existing TypeScript errors in the codebase
2. Run `yarn build` successfully
3. Run `yarn medusa db:migrate`

#### Option 2: Skip Build and Use Development Mode
1. The settings module works in development mode (server is running)
2. Create settings manually through admin API calls
3. Test the functionality without database migration

### 📋 **Manual Testing (Without Migration):**
You can test the admin interface by:
1. Go to `http://localhost:9000/app/settings`
2. Try to access the settings page
3. Use browser developer tools to test API calls

---

## 🔄 **How Changes Take Effect**

- **Settings Changes**: Immediate (refresh page to see)
- **Product Changes**: Immediate (refresh page to see)
- **No Server Restart**: Required for any changes
- **No Code Changes**: Required for content updates

---

## 📝 **Current Limitations & Next Steps**

### What Works Now:
- ✅ Admin interface for settings management
- ✅ Backend API endpoints
- ✅ Frontend integration (with API key)
- ✅ Database model and service

### What Needs Setup:
- ⚠️ Database migration for settings table
- ⚠️ Publishable API key configuration
- ⚠️ Frontend API key integration

### Future Enhancements:
- 📋 More homepage settings (colors, layout)
- 📋 Category-specific settings
- 📋 Settings validation and preview
- 📋 Settings backup/restore

---

## 🎉 **Once Setup is Complete**

Your main store page will be fully configurable through the admin panel. You'll be able to:
- ✅ Change videos anytime
- ✅ Add/edit book products
- ✅ Update descriptions and pricing
- ✅ Manage chapter content
- ✅ Modify store settings

All changes will be immediate and require no technical knowledge!

---

## 📚 **Product Images Configuration**

### **How to Add Product Cover Images**

The main page now displays product cover images from the MedusaJS admin panel.

#### **📋 Step-by-Step Instructions:**

1. **Go to Admin Panel**:
   - Open `http://localhost:9000/app`
   - Login with your admin credentials

2. **Navigate to Products**:
   - Click **"Products"** in the left sidebar
   - Select the product you want to add an image to

3. **Add Product Image**:
   - In the product details page, look for the **"Media"** section
   - Click **"Add Image"** or **"Upload"**
   - Upload your book cover image (recommended: 400x520px, JPG/PNG)
   - Save the product

4. **Image Requirements**:
   - **Format**: JPG, PNG, WebP
   - **Recommended size**: 400x520px (book cover ratio)
   - **Max file size**: 5MB
   - **Quality**: High resolution for best display

#### **🎨 Image Display Features:**

- **Automatic fallback**: Shows book icon (📖) if image fails to load
- **Loading states**: Smooth fade-in when image loads
- **Responsive design**: Scales properly on mobile devices
- **Error handling**: Graceful fallback to default icon

#### **📝 Best Practices:**

- **Use consistent aspect ratio** for all book covers
- **Optimize images** for web (compress without losing quality)
- **Use descriptive alt text** (automatically uses product title)
- **Test images** on different devices and screen sizes

#### **🔧 Troubleshooting:**

**Image not showing:**
1. Check image URL is accessible
2. Verify image format is supported
3. Check browser console for errors
4. Ensure image file size is under 5MB

**Image quality issues:**
1. Use higher resolution source images
2. Compress images using tools like TinyPNG
3. Consider using WebP format for better compression

The main page will automatically display product images once they're uploaded to the admin panel!

---

## 📚 **Table of Contents (Chapters) Configuration**

### **How to Set Up Book Chapters Using Collections**

The main page now displays interactive table of contents for each book using MedusaJS collections.

#### **📋 Step-by-Step Instructions:**

1. **Create a Collection for Chapters**:
   - Go to Admin Panel: `http://localhost:9000/app`
   - Navigate to **"Collections"** in the left sidebar
   - Click **"Create Collection"**
   - Name it: `[book-handle]-chapters` (e.g., `hebrew-basics-chapters`)
   - Add description: "Chapters for [Book Name]"
   - Save the collection

2. **Create Chapter Products**:
   - Go to **"Products"** section
   - Click **"Add Product"** for each chapter
   - Set product details:
     - **Title**: Chapter name (e.g., "Chapter 1: Hebrew Alphabet")
     - **Description**: Chapter description
     - **Price**: Chapter price (can be 0 for free chapters)
     - **Collection**: Select the chapters collection you created

3. **Configure Chapter Metadata**:
   - In each chapter product, go to **"Metadata"** section
   - Add these metadata fields:
     - `chapter_order`: `1`, `2`, `3`, etc. (for sorting)
     - `is_free`: `true` or `false` (for free chapters)
     - `chapter_duration`: `"15 min"` (reading time)
     - `chapter_difficulty`: `"beginner"`, `"intermediate"`, or `"advanced"`
     - `preview_content`: Short preview text

4. **Link Book to Chapters Collection**:
   - Edit your book product
   - In **"Metadata"** section, add:
     - `chapters_collection_id`: The ID or handle of your chapters collection

#### **🎨 Chapter Display Features:**

- **Interactive expansion**: Click chapters to see preview content
- **Free chapter indicators**: Shows "FREE" badge for free chapters
- **Difficulty levels**: Color-coded difficulty indicators
- **Chapter ordering**: Automatically sorted by `chapter_order`
- **Duration display**: Shows reading time for each chapter
- **Preview content**: Expandable chapter previews

#### **📝 Example Setup:**

**Book Product Metadata:**
```
chapters_collection_id: "hebrew-basics-chapters"
```

**Chapter Product Metadata:**
```
chapter_order: 1
is_free: true
chapter_duration: "15 min"
chapter_difficulty: "beginner"
preview_content: "Learn the Hebrew alphabet with interactive exercises..."
```

#### **🔧 Troubleshooting:**

**Chapters not showing:**
1. Check collection ID in book metadata
2. Verify chapters are in the correct collection
3. Ensure chapters have `chapter_order` metadata
4. Check browser console for error messages

**Chapters in wrong order:**
1. Set `chapter_order` metadata for each chapter (1, 2, 3, etc.)
2. Refresh the page to see updated order

The table of contents will automatically appear when a book has associated chapters!
