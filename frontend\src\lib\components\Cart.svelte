<script lang="ts">
	import {
		cartStore,
		cartActions,
		cartItemCount,
		cartTotal,
		cartLoading,
		cartSynced,
	} from "$lib/stores/cart";
	import { createEventDispatcher } from "svelte";
	import { goto } from "$app/navigation";
	import { _ } from "$lib/i18n";
	import type { Cart } from "$lib/types/products";
	import {
		formatPriceWithCurrency,
		currentCurrency,
	} from "$lib/stores/currency";
	import { isAuthenticated, currentCustomer } from "$lib/stores/auth";
	import AuthModal from "./AuthModal.svelte";

	export let isOpen = false;

	const dispatch = createEventDispatcher();
	let cart: Cart;
	let authModalOpen = false;
	let authMode: "login" | "register" = "login";

	$: cart = $cartStore;
	$: itemCount = $cartItemCount;
	$: total = $cartTotal;
	$: currency = $currentCurrency;

	function closeCart() {
		isOpen = false;
		dispatch("close");
	}

	function updateQuantity(productId: string, quantity: number) {
		cartActions.updateQuantity(productId, quantity);
	}

	async function removeItem(productId: string) {
		await cartActions.removeItem(productId);
	}

	function clearCart() {
		cartActions.clearCart();
	}

	function proceedToCheckout() {
		// Check if cart has items
		if (!cart.items || cart.items.length === 0) {
			alert($_("checkout.empty_cart"));
			return;
		}

		// Check if user is authenticated
		if (!$isAuthenticated) {
			// Show login modal
			authMode = "login";
			authModalOpen = true;
			return;
		}

		// Close cart and navigate to checkout
		closeCart();
		goto("/checkout");
	}

	function openAuthModal(mode: "login" | "register") {
		authMode = mode;
		authModalOpen = true;
	}

	function closeAuthModal() {
		authModalOpen = false;
	}

	function handleAuthSuccess() {
		// Close the auth modal
		authModalOpen = false;

		// Now proceed to checkout since user is authenticated
		if (cart.items && cart.items.length > 0) {
			closeCart();
			goto("/checkout");
		}
	}
</script>

<!-- Cart Overlay -->
{#if isOpen}
	<div class="fixed inset-0 z-50 overflow-hidden">
		<!-- Background overlay -->
		<div
			class="absolute inset-0 bg-black bg-opacity-50"
			on:click={closeCart}
		></div>

		<!-- Cart panel -->
		<div
			class="absolute right-0 top-0 h-full w-full max-w-lg shadow-xl"
			style="background-color: var(--color-background);"
		>
			<div class="flex h-full flex-col">
				<!-- Header -->
				<div class="flex items-center justify-between px-4 py-6">
					<div class="flex items-center space-x-2">
						<h1 class="font-title text-2xl">
							{$_("cart.title")}
						</h1>

						<!-- Cart status indicators -->
						{#if $cartLoading}
							<div
								class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"
							></div>
						{:else if $isAuthenticated && $cartSynced}
							<span
								class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
							>
								✓ Synced
							</span>
						{:else if $isAuthenticated && !$cartSynced}
							<span
								class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
							>
								⚠ Local
							</span>
						{:else}
							<span
								class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
							>
								📱 Guest
							</span>
						{/if}
					</div>

					<button
						type="button"
						class="btn-primary h-1 w-1"
						on:click={closeCart}
					>
						<span class="sr-only">Close cart</span>
						<svg
							class="h-2 w-auto"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M6 18L18 6M6 6l12 12"
							/>
						</svg>
					</button>
				</div>

				<!-- Cart items -->
				<div class="flex-1 overflow-y-auto px-4 py-6">
					{#if cart.items.length === 0}
						<div class="text-center">
							<img
								src="/images/rhino.png"
								alt="No items in cart"
								class="mx-auto h-20 w-auto"
							/>
							<h3 class="mt-2 text-sm font-medium">
								{$_("cart.empty_title")}
							</h3>
							<p class="font-additional">
								{$_("cart.empty_description")}
							</p>
							<div class="mt-6">
								<button
									type="button"
									class="btn-primary"
									on:click={closeCart}
								>
									{$_("cart.continue_shopping")}
								</button>
							</div>
						</div>
					{:else}
						<div class="space-y-4">
							{#each cart.items as item (item.product_id)}
								<div
									class="flex items-top p-4 border-t"
									style="border-color: var(--color-border);"
								>
									<!-- Product image -->
									<div
										class="h-20 w-16 flex-shrink-0 overflow-hidden rounded-md mr-4"
									>
										{#if item.product.cover_image}
											<img
												src={item.product.cover_image}
												alt={item.product.title}
												class="h-full w-full object-cover object-center"
											/>
										{:else}
											<div
												class="flex h-full w-full items-center justify-center"
												style="background-color: var(--color-bg-subtle);"
											>
												<svg
													class="h-8 w-8"
													style="color: var(--color-text-secondary);"
													fill="none"
													viewBox="0 0 24 24"
													stroke="currentColor"
												>
													<path
														stroke-linecap="round"
														stroke-linejoin="round"
														stroke-width="2"
														d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
													/>
												</svg>
											</div>
										{/if}
									</div>

									<!-- Product details -->
									<div class="flex-1 min-w-0 mr-4">
										<h4
											class="font-body text-lg font-medium mb-1"
											style="color: var(--color-text-primary);"
										>
											{item.product.title}
										</h4>
										<p
											class="font-body text-xs"
											style="color: var(--color-text-primary);"
										>
											{item.product.short_description ||
												"Book Description"}
										</p>
									</div>

									<!-- Quantity controls -->
									<div class="flex mr-4">
										<button
											type="button"
											class="w-8 h-8 rounded-full flex items-center justify-center transition-colors"
											style="background-color: var(--color-text-accent); color: white;"
											on:click={() =>
												updateQuantity(
													item.product_id,
													item.quantity - 1,
												)}
											aria-label="Decrease quantity"
										>
											<svg
												class="w-4 h-4"
												fill="none"
												stroke="currentColor"
												viewBox="0 0 24 24"
											>
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													stroke-width="2"
													d="M20 12H4"
												/>
											</svg>
										</button>
										<span
											class="mx-3 font-body text-lg font-medium"
											style="color: var(--color-text-primary);"
										>
											{item.quantity}
										</span>
										<button
											type="button"
											class="w-8 h-8 rounded-full flex items-center justify-center transition-colors"
											style="background-color: var(--color-text-accent); color: white;"
											on:click={() =>
												updateQuantity(
													item.product_id,
													item.quantity + 1,
												)}
											aria-label="Increase quantity"
										>
											<svg
												class="w-4 h-4"
												fill="none"
												stroke="currentColor"
												viewBox="0 0 24 24"
											>
												<path
													stroke-linecap="round"
													stroke-linejoin="round"
													stroke-width="2"
													d="M12 6v6m0 0v6m0-6h6m-6 0H6"
												/>
											</svg>
										</button>
									</div>

									<div class="grid justify-items-end">
										<!-- Price -->
										<div class="mr-4">
											<p
												class="font-title text-lg font-medium"
												style="color: var(--color-text-primary);"
											>
												{formatPriceWithCurrency(
													item.product.price,
												)}
											</p>
										</div>

										<!-- Remove button -->
										<button
											type="button"
											class="p-2 transition-opacity hover:opacity-70"
											on:click={() =>
												removeItem(item.product_id)}
										>
											<img
												src="/images/garbage dark.png"
												alt="Remove item"
												class="w-6 h-6"
											/>
										</button>
									</div>
								</div>
							{/each}
						</div>
					{/if}
				</div>

				<!-- Footer with total and checkout -->
				{#if cart.items.length > 0}
					<div class="border-t p-2"	style="border-color: var(--color-border);"
>
						<!-- Discount section -->
						{#if cart.discount_amount}
							<div
								class="flex justify-between text-sm text-gray-600 mb-2"
							>
								<span>Subtotal:</span>
								<span
									>{formatPriceWithCurrency(
										total + cart.discount_amount,
									)}</span
								>
							</div>
							<div
								class="flex justify-between text-sm text-green-600 mb-2"
							>
								<span>Discount ({cart.discount_code}):</span>
								<span
									>-{formatPriceWithCurrency(
										cart.discount_amount,
									)}</span
								>
							</div>
						{/if}

						<!-- Total -->
						<div
							class="flex justify-between text-base font-medium mb-6"
						>
							<span>{$_("cart.total")}</span>
							<span>{formatPriceWithCurrency(total)}</span>
						</div>

						<div class="flex flex-col items-center">
							<!-- Checkout button -->
							<button
								type="button"
								class="btn-primary mb-4 w-60"
								on:click={proceedToCheckout}
							>
								{$_("cart.proceed_to_checkout")}
							</button>

							<!-- Continue shopping -->
							<button
								type="button"
								class="btn-secondary w-60"
								on:click={closeCart}
							>
								{$_("cart.continue_shopping")}
							</button>
						</div>
					</div>
				{/if}
			</div>
		</div>
	</div>
{/if}

<!-- Authentication Modal -->
<AuthModal
	bind:isOpen={authModalOpen}
	bind:mode={authMode}
	on:close={closeAuthModal}
	on:success={handleAuthSuccess}
/>
