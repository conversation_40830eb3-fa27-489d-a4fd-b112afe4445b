# Testing Tranzila Integration with Mock Server

This guide will help you test the Tranzila payment integration using a mock server that simulates the Tranzila API.

## Setup Instructions

### 1. Install Mock Server Dependencies

```bash
# Copy the package.json for mock server
cp mock-server-package.json package.json

# Install dependencies
npm install

# Rename back
mv package.json mock-package.json
```

Or manually install:
```bash
npm install express cors body-parser node-fetch nodemon
```

### 2. Start the Mock Tranzila Server

```bash
node mock-tranzila-server.js
```

You should see:
```
🚀 Mock Tranzila Server running on http://localhost:3001
📋 Payment endpoint: http://localhost:3001/cgi-bin/tranzila71u.cgi
🔔 Webhook endpoint: http://localhost:3001/webhook-notify

🧪 Test Cards:
   Success: ****************
   Failure: ****************
   Expiry: Any future date
   CVV: Any 3 digits
```

### 3. Start Your Backend

```bash
cd backend
yarn dev
```

### 4. Start Your Frontend

```bash
cd frontend
yarn dev
```

## Testing the Integration

### Test Flow:

1. **Go to your frontend**: http://localhost:5173
2. **Add items to cart** and proceed to checkout
3. **Select Tranzila payment** (should be the only option available)
4. **Click "Proceed to Payment"**
5. **You'll be redirected** to the mock Tranzila payment page
6. **Use test card numbers**:
   - **Success**: `****************`
   - **Failure**: `****************`
   - **Expiry**: Any future date (e.g., `12/25`)
   - **CVV**: Any 3 digits (e.g., `123`)

### Expected Behavior:

#### Successful Payment:
1. Mock payment page shows with test card instructions
2. Enter success card number: `****************`
3. Fill other fields with any valid data
4. Click "Complete Payment"
5. Processing delay (3 seconds)
6. Webhook sent to your backend
7. Redirect to success page
8. Order should be created

#### Failed Payment:
1. Enter failure card number: `****************`
2. Processing delay
3. Webhook sent with error status
4. Redirect to error page
5. No order created

## Testing Both Modes

### Redirect Mode (Default):
- Set `TRANZILA_IFRAME_MODE=false` in backend/.env
- Full page redirect to mock payment page

### iFrame Mode:
- Set `TRANZILA_IFRAME_MODE=true` in backend/.env
- Payment form embedded in your checkout page

## Monitoring

### Backend Logs:
Watch for:
```
🔄 Generating Tranzila payment URL for cart: cart_xxx
✅ Tranzila payment URL generated: http://localhost:3001/cgi-bin/tranzila71u.cgi?...
🔔 TRANZILA WEBHOOK RECEIVED
📦 Tranzila Response Data: {...}
✅ Payment authorized successfully
```

### Mock Server Logs:
Watch for:
```
🔄 Mock Tranzila Payment Request: {...}
🔔 Sending webhook notification to: http://localhost:9000/webhooks/tranzila
✅ Webhook sent, status: 200
```

### Frontend Console:
Watch for:
```
🔄 Generating Tranzila payment URL for cart: cart_xxx
✅ Tranzila payment URL generated
🔄 Redirecting to Tranzila payment page
```

## Troubleshooting

### Mock Server Not Starting:
- Check if port 3001 is available
- Install dependencies: `npm install express cors body-parser node-fetch`

### Payment URL Not Generated:
- Check backend logs for errors
- Verify `PAYMENT_PROVIDER=tranzila` in backend/.env
- Ensure Tranzila service is loading correctly

### Webhook Not Received:
- Check if backend is running on port 9000
- Verify `MEDUSA_BACKEND_URL=http://localhost:9000` in .env
- Check backend webhook route is accessible

### Redirect Not Working:
- Verify `FRONTEND_URL=http://localhost:5173` in .env
- Check frontend success/error pages exist
- Ensure CORS is configured correctly

## Mock Server Features

The mock server provides:

1. **Realistic Payment Form**: Mimics Tranzila's payment interface
2. **Test Card Validation**: Different responses based on card numbers
3. **Webhook Simulation**: Sends webhooks to your backend
4. **Both Modes**: Supports redirect and iframe modes
5. **Processing Delays**: Simulates real payment processing time
6. **Detailed Logging**: Shows all requests and responses

## Next Steps

Once testing is complete with the mock server:

1. **Get Real Tranzila Credentials**: Contact Tranzila for merchant account
2. **Update Environment Variables**: Replace mock values with real credentials
3. **Switch to Production URLs**: Update `testUrl` in service to real Tranzila sandbox
4. **Test with Real Tranzila**: Use their test environment
5. **Deploy to Production**: Use production Tranzila URLs and credentials

The mock server helps you verify that your integration logic is working correctly before dealing with real payment provider setup!
