#!/usr/bin/env node

/**
 * Simple test script to call the debug endpoint
 */

const http = require('http')

const options = {
  hostname: 'localhost',
  port: 9000,
  path: '/admin/digital-products/debug',
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
}

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`)
  console.log(`Headers: ${JSON.stringify(res.headers)}`)
  
  let data = ''
  res.on('data', (chunk) => {
    data += chunk
  })
  
  res.on('end', () => {
    try {
      const parsed = JSON.parse(data)
      console.log('\n📊 DEBUG RESPONSE:')
      console.log(JSON.stringify(parsed, null, 2))
    } catch (error) {
      console.log('\n📄 RAW RESPONSE:')
      console.log(data)
    }
  })
})

req.on('error', (error) => {
  console.error('❌ Error:', error)
})

req.end()
