#!/bin/bash

# Official MedusaJS startup script based on documentation
# https://docs.medusajs.com/learn/deployment/general

echo "🚀 MedusaJS Official Startup Process - $(date)"
echo "📂 Working directory: $(pwd)"

# Check directory contents
echo "📁 Directory contents:"
ls -la

# Official MedusaJS deployment process
if [ -d ".medusa/server" ]; then
    echo "✅ Found .medusa/server - following official deployment guide"
    echo "📋 Official command: cd .medusa/server && yarn install && yarn predeploy && yarn start"

    cd .medusa/server
    echo "� Now in: $(pwd)"
    echo "📁 Built server contents:"
    ls -la

    echo "📦 Checking production dependencies..."
    # Check if dependencies are already pre-installed
    if [ -d "node_modules" ] && [ -f "node_modules/.yarn-integrity" ]; then
        echo "✅ Dependencies already pre-installed during build, skipping installation"
    elif [ -f "package.json" ]; then
        echo "⚠️  Dependencies not pre-installed, installing now (this may timeout)..."
        yarn install --production --frozen-lockfile --silent --network-timeout 300000 --network-concurrency 1 --ignore-scripts
    else
        echo "❌ No package.json found in .medusa/server"
        echo "⚠️  Using parent directory dependencies..."
        cd ..
        if [ ! -d "node_modules" ]; then
            yarn install --production --frozen-lockfile --silent --network-timeout 300000 --network-concurrency 1 --ignore-scripts
        else
            echo "✅ Parent dependencies already available"
        fi
        cd .medusa/server
    fi

    echo "🔄 Running predeploy (migrations)..."
    yarn predeploy || echo "⚠️  Predeploy failed, continuing..."

    # Clean up tsconfig.json after dependencies and migrations are done
    if [ -f "tsconfig.json" ]; then
        echo "🧹 Removing tsconfig.json from runtime directory (no longer needed)"
        rm tsconfig.json
    fi

    echo "🎯 Starting MedusaJS server..."
    exec yarn start

else
    echo "❌ No .medusa/server directory found!"
    echo "⚠️  This means the build process didn't work correctly."
    echo "📋 Attempting fallback startup from root..."

fi
