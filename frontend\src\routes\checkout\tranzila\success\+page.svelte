<script lang="ts">
  import { onMount } from 'svelte'
  import { goto } from '$app/navigation'
  import { page } from '$app/stores'
  import { _ } from '$lib/i18n'
  import { apiClient } from '$lib/api/client'
  import { cartActions } from '$lib/stores/cart'

  let isProcessing = true
  let error = ''
  let order: any = null
  let transactionDetails: any = {}

  onMount(async () => {
    // Get transaction details from URL parameters
    const urlParams = $page.url.searchParams
    transactionDetails = {
      transactionId: urlParams.get('transaction_id'),
      responseCode: urlParams.get('response_code'),
      amount: urlParams.get('amount'),
      currency: urlParams.get('currency'),
      confirmationCode: urlParams.get('confirmation_code'),
      message: urlParams.get('message'),
    }

    console.log('🎉 Tranzila payment success callback:', transactionDetails)

    // Get cart ID from session storage
    const cartId = sessionStorage.getItem('tranzila_cart_id')
    if (!cartId) {
      error = $_('checkout.cart_not_found')
      isProcessing = false
      return
    }

    try {
      // Complete the cart to create the order
      console.log('🔄 Completing cart after successful Tranzila payment:', cartId)
      const completeResponse = await apiClient.completeCart(cartId)
      
      if (completeResponse.data?.type === 'order' && completeResponse.data.order) {
        order = completeResponse.data.order
        console.log('✅ Order created successfully:', order.id)
        
        // Clear cart and session storage
        cartActions.initialize()
        sessionStorage.removeItem('tranzila_cart_id')
        sessionStorage.removeItem('tranzila_transaction_id')
        
        // Redirect to order confirmation page after a short delay
        setTimeout(() => {
          goto(`/orders/${order.id}/confirmation`)
        }, 3000)
      } else {
        throw new Error('Failed to create order')
      }
    } catch (err) {
      console.error('❌ Error completing order:', err)
      error = err instanceof Error ? err.message : $_('checkout.order_completion_failed')
    } finally {
      isProcessing = false
    }
  })
</script>

<svelte:head>
  <title>{$_('checkout.payment_successful')} - Hebrew Book Store</title>
</svelte:head>

<div class="success-page">
  <div class="container">
    {#if isProcessing}
      <div class="processing">
        <div class="spinner"></div>
        <h1>{$_('checkout.processing_payment')}</h1>
        <p>{$_('checkout.please_wait')}</p>
      </div>
    {:else if error}
      <div class="error">
        <div class="error-icon">❌</div>
        <h1>{$_('checkout.payment_processing_error')}</h1>
        <p>{error}</p>
        <div class="actions">
          <button on:click={() => goto('/checkout')} class="button primary">
            {$_('checkout.return_to_checkout')}
          </button>
          <button on:click={() => goto('/')} class="button secondary">
            {$_('common.home')}
          </button>
        </div>
      </div>
    {:else if order}
      <div class="success">
        <div class="success-icon">✅</div>
        <h1>{$_('checkout.payment_successful')}</h1>
        <p>{$_('checkout.order_created_successfully')}</p>
        
        <div class="order-details">
          <h2>{$_('checkout.order_details')}</h2>
          <div class="detail-row">
            <span class="label">{$_('checkout.order_number')}:</span>
            <span class="value">{order.display_id || order.id}</span>
          </div>
          <div class="detail-row">
            <span class="label">{$_('checkout.transaction_id')}:</span>
            <span class="value">{transactionDetails.transactionId}</span>
          </div>
          <div class="detail-row">
            <span class="label">{$_('checkout.confirmation_code')}:</span>
            <span class="value">{transactionDetails.confirmationCode}</span>
          </div>
          <div class="detail-row">
            <span class="label">{$_('checkout.amount')}:</span>
            <span class="value">{transactionDetails.amount} {transactionDetails.currency}</span>
          </div>
        </div>

        <div class="redirect-notice">
          <p>{$_('checkout.redirecting_to_confirmation')}</p>
          <div class="countdown">3</div>
        </div>

        <div class="actions">
          <button on:click={() => goto(`/orders/${order.id}/confirmation`)} class="button primary">
            {$_('checkout.view_order')}
          </button>
          <button on:click={() => goto('/')} class="button secondary">
            {$_('common.continue_shopping')}
          </button>
        </div>
      </div>
    {/if}
  </div>
</div>

<style>
  .success-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-color);
    padding: 2rem;
  }

  .container {
    max-width: 600px;
    width: 100%;
    text-align: center;
  }

  .processing, .error, .success {
    background: white;
    padding: 3rem 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .spinner {
    width: 60px;
    height: 60px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 2rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .success-icon, .error-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
  }

  h1 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 2rem;
  }

  .error h1 {
    color: var(--error-color);
  }

  p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-size: 1.1rem;
  }

  .order-details {
    background: var(--bg-light);
    padding: 1.5rem;
    border-radius: 8px;
    margin: 2rem 0;
    text-align: left;
  }

  .order-details h2 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.3rem;
    text-align: center;
  }

  .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-light);
  }

  .detail-row:last-child {
    border-bottom: none;
  }

  .label {
    font-weight: 600;
    color: var(--text-color);
  }

  .value {
    color: var(--text-secondary);
    font-family: monospace;
  }

  .redirect-notice {
    background: var(--info-bg);
    border: 1px solid var(--info-border);
    color: var(--info-color);
    padding: 1rem;
    border-radius: 6px;
    margin: 2rem 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
  }

  .countdown {
    background: var(--primary-color);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    animation: countdown 3s linear;
  }

  @keyframes countdown {
    0% { transform: scale(1); }
    33% { transform: scale(1.1); }
    66% { transform: scale(1.1); }
    100% { transform: scale(1); }
  }

  .actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
  }

  .button {
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    border: none;
  }

  .button.primary {
    background: var(--primary-color);
    color: white;
  }

  .button.primary:hover {
    background: var(--primary-hover);
  }

  .button.secondary {
    background: var(--secondary-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }

  .button.secondary:hover {
    background: var(--hover-bg);
  }

  @media (max-width: 768px) {
    .success-page {
      padding: 1rem;
    }

    .processing, .error, .success {
      padding: 2rem 1rem;
    }

    .actions {
      flex-direction: column;
    }

    .button {
      width: 100%;
    }
  }
</style>
