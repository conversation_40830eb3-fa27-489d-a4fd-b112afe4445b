# AllPay Payment Provider Configuration
# Copy this to your .env file and update with your actual AllPay credentials

# Required: AllPay API Credentials
# Get these from your AllPay account: Settings → API Integrations
ALLPAY_LOGIN=your_allpay_login_here
ALLPAY_API_KEY=your_allpay_api_key_here

# Optional: Payment Configuration
ALLPAY_CURRENCY=ILS
ALLPAY_LANGUAGE=AUTO
ALLPAY_TEST_MODE=true
ALLPAY_IFRAME_MODE=true

# Required: Application URLs
# These are used for AllPay callbacks and redirects
MEDUSA_BACKEND_URL=http://localhost:9000
FRONTEND_URL=http://localhost:5173

# Optional: AllPay Advanced Settings
# ALLPAY_WEBHOOK_SECRET=your_webhook_secret_if_using_custom_verification
# ALLPAY_TIMEOUT=3600

# Notes:
# - ALLPAY_LOGIN: Your login from AllPay API Integrations section
# - <PERSON><PERSON><PERSON><PERSON>_API_KEY: Your API key from AllPay API Integrations section  
# - ALLPAY_CURRENCY: Default currency (ILS, USD, EUR)
# - ALLPAY_LANGUAGE: Payment page language (AUTO, EN, HE, RU, AR)
# - ALLPAY_TEST_MODE: Set to false for production
# - ALLPAY_IFRAME_MODE: true for iframe integration, false for redirect
# - MEDUSA_BACKEND_URL: Your backend URL for webhooks
# - FRONTEND_URL: Your frontend URL for redirects
