# Hero Video Setup Guide

This guide explains how to set up the full-screen hero video section on your Hebrew Book Store homepage.

## 🎬 Hero Video Feature

The hero video section appears at the very top of your main page as a full-screen, auto-playing background video with overlay text and call-to-action button, similar to the Ulpan Morasha website.

### Features:
- **Full-screen video background** that covers the entire viewport
- **Auto-play with mute** for better user experience
- **Responsive design** that works on all devices
- **Overlay content** with title, subtitle, and CTA button
- **Smooth scroll** to the about section when CTA is clicked
- **Multilingual support** (English/Hebrew)

## 🔧 Configuration Steps

### Step 1: Access MedusaJS Admin Panel

1. **Start your backend server**:
   ```bash
   cd backend
   yarn dev
   ```

2. **Open admin panel**:
   - Navigate to: `http://localhost:9000/app`
   - Login with your admin credentials

### Step 2: Configure Hero Video URL

1. **Navigate to Store Settings**:
   - Go to `Settings` → `Store Details`
   - Or click on the store configuration section

2. **Add Hero Video URL to Metadata**:
   - Click the edit icon (⚙️) next to **"Metadata"**
   - In the metadata editor, add:
     - **Key**: `hero_video_url`
     - **Value**: Your YouTube embed URL

3. **YouTube URL Format**:
   - ✅ **Correct format**: `https://www.youtube.com/embed/YOUR_VIDEO_ID`
   - ❌ **Don't use**: `https://www.youtube.com/watch?v=YOUR_VIDEO_ID`

4. **Example Configuration**:
   ```json
   {
     "hero_video_url": "https://www.youtube.com/embed/dQw4w9WgXcQ",
     "main_video_url": "https://www.youtube.com/embed/ANOTHER_VIDEO_ID"
   }
   ```

5. **Save Changes**:
   - Click **"Save"** to apply the new video URL
   - The hero video will appear on your store's main page

### Step 3: Get YouTube Embed URL

1. **Find your video on YouTube**
2. **Click "Share" button**
3. **Click "Embed" option**
4. **Copy the URL from the iframe src attribute**
   - Example: `https://www.youtube.com/embed/dQw4w9WgXcQ`

### Step 4: Test the Hero Video

1. **Refresh your frontend**:
   - Navigate to: `http://localhost:5173`
   - The hero video should appear at the top of the page

2. **Check browser console**:
   - Open developer tools (`F12`)
   - Look for log messages about hero video loading
   - Verify no CORS or loading errors

## 🎨 Customization Options

### Text Content

The hero section text is controlled by translation files:

**English** (`frontend/src/lib/i18n/locales/en.json`):
```json
{
  "hero": {
    "title": "Learn Hebrew with Confidence",
    "subtitle": "Master Hebrew through our innovative interactive learning platform designed for modern learners",
    "cta_button": "Start Your Journey"
  }
}
```

**Hebrew** (`frontend/src/lib/i18n/locales/he.json`):
```json
{
  "hero": {
    "title": "למדו עברית בביטחון",
    "subtitle": "שלטו בעברית דרך פלטפורמת הלמידה האינטראקטיבית החדשנית שלנו המיועדת ללומדים מודרניים",
    "cta_button": "התחילו את המסע שלכם"
  }
}
```

### Styling

The hero video section includes:
- **Full viewport height** (`100vh`)
- **Responsive video scaling** for all screen sizes
- **Dark overlay** (30% opacity) for text readability
- **Centered content** with proper spacing
- **Smooth animations** and transitions

## 🔍 Troubleshooting

### Video Not Showing

1. **Check store settings**:
   - Verify `hero_video_url` is set in store metadata
   - Ensure URL format is correct (embed URL, not watch URL)

2. **Check browser console**:
   - Look for error messages about video loading
   - Verify API calls to `/public/settings` are successful

3. **Check CORS configuration**:
   - Ensure `STORE_CORS` includes your frontend URL
   - Restart backend server after CORS changes

### Video Not Auto-playing

1. **Browser policies**: Modern browsers may block autoplay
2. **Mute parameter**: Video includes `mute=1` for autoplay compatibility
3. **User interaction**: Some browsers require user interaction first

### Mobile Issues

1. **iOS Safari**: May not support autoplay even with mute
2. **Data usage**: Consider showing static image on mobile
3. **Performance**: Video may impact mobile performance

## 🚀 Advanced Configuration

### Multiple Video Sources

You can configure different videos for different purposes:

```json
{
  "hero_video_url": "https://www.youtube.com/embed/HERO_VIDEO_ID",
  "main_video_url": "https://www.youtube.com/embed/ABOUT_VIDEO_ID",
  "sample_book_url": "https://online.fliphtml5.com/SAMPLE_BOOK/"
}
```

### Video Parameters

The hero video includes these YouTube parameters:
- `autoplay=1` - Auto-start the video
- `mute=1` - Start muted (required for autoplay)
- `loop=1` - Loop the video continuously
- `controls=0` - Hide video controls
- `showinfo=0` - Hide video info
- `rel=0` - Don't show related videos
- `modestbranding=1` - Minimize YouTube branding
- `playsinline=1` - Play inline on mobile

## 📱 Mobile Considerations

The hero video is fully responsive and includes:
- **Aspect ratio preservation** on all screen sizes
- **Touch-friendly** call-to-action button
- **Optimized text sizes** for mobile devices
- **Smooth scrolling** to content sections

---

## 🎯 Result

After configuration, your homepage will feature:
1. **Full-screen hero video** at the top
2. **Overlay content** with your branding
3. **Smooth transition** to the existing about section
4. **Professional appearance** similar to Ulpan Morasha

The hero video section will only appear if `hero_video_url` is configured in your store settings. If not configured, the page will start with the existing about section as before.
