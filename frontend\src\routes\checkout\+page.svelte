<script lang="ts">
  import { onMount } from 'svelte'
  import { goto } from '$app/navigation'
  import { authStore, isAuthenticated, currentCustomer } from '$lib/stores/auth'
  import { cartStore, cartActions } from '$lib/stores/cart'
  import { apiClient } from '$lib/api/client'
  import { _ } from '$lib/i18n'
  import { getCurrentLocale, isRTL } from '$lib/i18n'
  import CheckoutSteps from '$lib/components/checkout/CheckoutSteps.svelte'
  import EmailStep from '$lib/components/checkout/EmailStep.svelte'
  import BillingAddressStep from '$lib/components/checkout/BillingAddressStep.svelte'
  import PaymentStep from '$lib/components/checkout/PaymentStep.svelte'
 	import { formatPriceWithCurrency } from '$lib/stores/currency'

  let currentLocale = 'en'
  let rtl = false
  
  // Checkout state
  let currentStep = 1
  let isLoading = false
  let error = ''
  
  // Cart data
  let cart: any = null
  
  // Checkout data (simplified for digital products)
  let checkoutData = {
    email: '',
    billing_address: {
      first_name: '',
      last_name: '',
      address_1: '',
      address_2: '',
      city: '',
      postal_code: '',
      country_code: 'il',
      phone: ''
    },
    payment_provider: null as string | null
  }

  $: currentLocale = getCurrentLocale()
  $: rtl = isRTL(currentLocale)
  $: cart = $cartStore

  // Redirect to home if not authenticated
  $: if (!$isAuthenticated && typeof window !== 'undefined') {
    goto('/')
  }

  // Note: Removed automatic redirect to cart when empty to prevent issues during checkout completion
  // Users can manually navigate to cart if needed

  onMount(async () => {
    // Initialize auth state from localStorage with token validation
    await authStore.initialize()

    // Redirect to home if not authenticated
    if (!$isAuthenticated) {
      goto('/')
      return
    }

    // Initialize cart
    await cartActions.initialize()

    // Pre-fill customer data if available
    if ($currentCustomer) {
      checkoutData.email = $currentCustomer.email || ''

      // If customer email is available and cart doesn't have email, skip email step
      if (checkoutData.email && (!cart?.email || cart.email !== checkoutData.email)) {
        // Auto-update cart with customer email
        if (cart?.id) {
          apiClient.updateCartEmail(cart.id, checkoutData.email).then(response => {
            if (response.data?.cart) {
              cartActions.updateFromBackendCart(response.data.cart)
            }
          }).catch(console.error)
        }
        // Skip to address step since email is already known
        currentStep = 2
      }

      // Pre-fill billing address with customer data
      checkoutData.billing_address.first_name = $currentCustomer.first_name || ''
      checkoutData.billing_address.last_name = $currentCustomer.last_name || ''
    }
  })

  function nextStep() {
    if (currentStep < 3) {  // Only 3 steps now: Email -> Billing -> Payment
      currentStep++
    }
  }

  function prevStep() {
    if (currentStep > 1) {
      currentStep--
    }
  }

  async function handleEmailSubmit(email: string) {
    try {
      isLoading = true
      error = ''
      
      if (!cart?.id) {
        throw new Error('No cart found')
      }

      // Update cart with email
      const response = await apiClient.updateCartEmail(cart.id, email)
      if (response.data?.cart) {
        cartActions.updateFromBackendCart(response.data.cart)
        checkoutData.email = email
        nextStep()
      }
    } catch (err) {
      console.error('Failed to update email:', err)
      error = 'Failed to update email. Please try again.'
    } finally {
      isLoading = false
    }
  }

  async function handleBillingAddressSubmit(billingAddress: any) {
    try {
      isLoading = true
      error = ''

      console.log('Billing address submitted:', billingAddress)

      if (!cart?.id) {
        throw new Error('No cart found')
      }

      // Update cart with billing address only (no shipping for digital products)
      const response = await apiClient.updateCartAddresses(cart.id, {
        billing_address: billingAddress
      })
      if (response.data?.cart) {
        cartActions.updateFromBackendCart(response.data.cart)
        checkoutData.billing_address = billingAddress
        nextStep()
      }
    } catch (err) {
      console.error('Failed to update billing address:', err)
      error = 'Failed to update billing address. Please try again.'
    } finally {
      isLoading = false
    }
  }

  async function handlePaymentSubmit(paymentProvider: string) {
    try {
      isLoading = true
      error = ''

      if (!cart?.id) {
        throw new Error('No cart found')
      }

      // Payment provider selection is handled in PaymentStep component
      checkoutData.payment_provider = paymentProvider
      console.log('✅ Payment provider selected:', paymentProvider)
    } catch (err) {
      console.error('Failed to set payment provider:', err)
      error = 'Failed to set payment provider. Please try again.'
    } finally {
      isLoading = false
    }
  }
</script>

<svelte:head>
  <title>{$_('checkout.page_title')} - Hebrew Book Store</title>
  <meta name="description" content={$_('checkout.page_description')} />
</svelte:head>

<div class="min-h-screen sm:px-6 lg:px-8" style="background: var(--color-bg-primary);">
    {#if $isAuthenticated && $currentCustomer && cart && cart.items.length > 0}
      <!-- Page Header -->
      <div class="header-div sm:px-6 lg:px-8 py-4">
        <h1 class="text-4xl font-medium" class:text-right={rtl}>
          {$_('checkout.title')}
        </h1>
      </div>

      <!-- Checkout Steps -->
      <CheckoutSteps {currentStep} />

      <!-- Error Message -->
      {#if error}
        <div class="mb-6 px-8 bg-red-50 border border-red-200 rounded-lg">
          <p class="text-red-800 font-book-text">{error}</p>
        </div>
      {/if}

      <!-- Checkout Content -->
      <div class="grid lg:grid-cols-3 gap-8 px-8">
        <!-- Checkout Steps -->
        <div class="lg:col-span-2">
          {#if currentStep === 1}
            <EmailStep
              email={checkoutData.email}
              {isLoading}
              onSubmit={handleEmailSubmit}
            />
          {:else if currentStep === 2}
            <BillingAddressStep
              billing_address={checkoutData.billing_address}
              {isLoading}
              onSubmit={handleBillingAddressSubmit}
              onBack={prevStep}
            />
          {:else if currentStep === 3}
            <PaymentStep
              {cart}
              selectedProvider={checkoutData.payment_provider}
              {isLoading}
              onSubmit={handlePaymentSubmit}
              onBack={prevStep}
            />
          {/if}
        </div>

        <!-- Order Summary -->
        <div class="lg:col-span-1">
          <div class="card-classic p-6 sticky top-8">
            <h3 class="font-book-title text-2xl font-medium mb-4" style="color: var(--color-text-primary); text-align: center;">
              {$_('checkout.order_summary')}
            </h3>
            
            <!-- Cart Items -->
            <div class="space-y-4 mb-10">
              {#each cart.items as item, index (item.id || item.product_id || item.product?.id || index)}
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <div class="w-auto h-12 flex items-center justify-center overflow-hidden">
                      {#if item.product.cover_image}
                        <img
                          src={item.product.cover_image}
                          alt={item.product.title}
                          class="w-full h-full object-cover"
                        />
                      {:else}
                        <span class="text-lg">
                          {#if item.product.type?.toLowerCase() === 'book'}
                            📚
                          {:else if item.product.type?.toLowerCase() === 'chapter'}
                            📖
                          {:else}
                            📄
                          {/if}
                        </span>
                      {/if}
                    </div>
                  </div>
                  <div class="flex-1 min-w-0">
                    <p class="font-book-text text-lg font-normal truncate" style="color: var(--color-text-primary);">
                      {item.product.title}
                    </p>
                    <p class="font-book-text text-sm italic" style="color: var(--color-text-primary); ">
                      {$_('cart.quantity')} {item.quantity}
                    </p>
                  </div>
                  <div class="text-right">
                    <p class="font-book-text text-lg font-normal" style="color: var(--color-text-primary);">
                      {item.unit_price ? formatPriceWithCurrency(item.unit_price * item.quantity) :
                        item.product.price ? formatPriceWithCurrency(item.product.price * item.quantity) : '0.00'}
                    </p>
                  </div>
                </div>
              {/each}
            </div>

            <!-- Order Totals -->
            <div class="border-t pt-2 " style="border-color: var(--color-primary); ">
              {#if cart.tax_total}
                <div class="flex justify-between">
                  <span class="font-book-text" style="color: var(--color-text-secondary);">{$_('checkout.tax')}</span>
                  <span class="font-book-text" style="color: var(--color-text-primary);">
                    {cart.tax_total ? formatPriceWithCurrency(cart.tax_total) : '0.00'}
                  </span>
                </div>
              {/if}
              <div class="flex justify-between text-lg font-medium ">
                <span class="font-book-title" style="color: var(--color-text-primary);">{$_('checkout.total')}</span>
                <span class="font-book-title" style="color: var(--color-text-primary);">
                  {cart.total ? formatPriceWithCurrency(cart.total) : cart.total_amount ? 
                    formatPriceWithCurrency(cart.total_amount) : '0.00'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

    {:else if !$isAuthenticated}
      <!-- Not Authenticated -->
      <div class="text-center py-16">
        <div class="text-6xl mb-6">🔒</div>
        <h1 class="font-book-title text-2xl font-bold mb-4" style="color: var(--color-text-primary);">
          {$_('checkout.login_required')}
        </h1>
        <p class="font-book-text text-lg mb-8" style="color: var(--color-text-secondary);">
          {$_('checkout.please_sign_in')}
        </p>
        <a href="/" class="btn-classic inline-block">
          {$_('checkout.go_home')}
        </a>
      </div>
    {:else}
      <!-- Empty Cart -->
      <div class="text-center py-16">
        <div class="text-6xl mb-6">🛒</div>
        <h1 class="font-book-title text-2xl font-bold mb-4" style="color: var(--color-text-primary);">
          {$_('checkout.empty_cart')}
        </h1>
        <p class="font-book-text text-lg mb-8" style="color: var(--color-text-secondary);">
          {$_('checkout.add_items_to_cart')}
        </p>
        <a href="/catalog" class="btn-classic inline-block">
          {$_('checkout.browse_products')}
        </a>
      </div>
    {/if}
</div>
