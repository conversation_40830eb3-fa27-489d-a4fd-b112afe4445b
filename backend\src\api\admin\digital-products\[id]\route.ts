import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@medusajs/framework/http"
import {
  ContainerRegistrationKeys,
} from "@medusajs/framework/utils"
import { DIGITAL_PRODUCT_MODULE } from "../../../../modules/digital-product"

// GET /admin/digital-products/[id] - Get a specific digital product
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  const { id } = req.params
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY)
  const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER)

  try {
    logger.info(`📄 Fetching digital product: ${id}`)

    const { data: digitalProducts } = await query.graph({
      entity: "digital_product",
      fields: [
        "id",
        "name", 
        "description",
        "product_variant_id",
        "medias.*",
        "product_variant.*",
        "product_variant.product.*",
        "created_at",
        "updated_at"
      ],
      filters: { id }
    })

    if (!digitalProducts || digitalProducts.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Digital product not found"
      })
    }

    const digitalProduct = digitalProducts[0]
    logger.info(`✅ Digital product found: ${digitalProduct.name}`)

    res.json({
      success: true,
      digital_product: digitalProduct
    })
  } catch (error) {
    logger.error(`❌ Error fetching digital product ${id}:`, error)
    res.status(500).json({
      success: false,
      message: "Failed to fetch digital product",
      error: error.message
    })
  }
}

// DELETE /admin/digital-products/[id] - Delete a digital product
export const DELETE = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  const { id } = req.params
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY)
  const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER)
  const digitalProductService = req.scope.resolve(DIGITAL_PRODUCT_MODULE)

  try {
    logger.info(`🗑️ Deleting digital product: ${id}`)

    // First, check if the digital product exists
    const { data: digitalProducts } = await query.graph({
      entity: "digital_product",
      fields: ["id", "name"],
      filters: { id }
    })

    if (!digitalProducts || digitalProducts.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Digital product not found"
      })
    }

    const digitalProduct = digitalProducts[0]
    logger.info(`📄 Found digital product to delete: ${digitalProduct.name}`)

    // Use the digital product service to delete (this handles cascading deletes)
    await digitalProductService.deleteDigitalProducts([id])

    logger.info(`✅ Digital product deleted successfully: ${digitalProduct.name}`)

    res.json({
      success: true,
      message: "Digital product deleted successfully",
      deleted_product: {
        id: digitalProduct.id,
        name: digitalProduct.name
      }
    })
  } catch (error) {
    logger.error(`❌ Error deleting digital product ${id}:`, error)
    res.status(500).json({
      success: false,
      message: "Failed to delete digital product",
      error: error.message
    })
  }
}

// PUT /admin/digital-products/[id] - Update a digital product
export const PUT = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  const { id } = req.params
  const body = req.body as { name?: string; description?: string; medias?: any[] }
  const { name, description, medias } = body
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY)
  const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER)

  try {
    logger.info(`📝 Updating digital product: ${id}`)

    // First, check if the digital product exists
    const { data: existingProducts } = await query.graph({
      entity: "digital_product",
      fields: ["id", "name"],
      filters: { id }
    })

    if (!existingProducts || existingProducts.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Digital product not found"
      })
    }

    // Update the digital product
    const updateData: any = {}
    if (name !== undefined) updateData.name = name
    if (description !== undefined) updateData.description = description

    if (Object.keys(updateData).length > 0) {
      // For now, we'll skip the update functionality as it requires more complex implementation
      // This can be implemented later when needed
      logger.info(`📝 Update requested for digital product ${id}, but update functionality is not yet implemented`)
    }

    // Handle media updates if provided
    if (medias && Array.isArray(medias)) {
      // This is a simplified approach - in a full implementation,
      // you might want to handle media updates more granularly
      logger.info(`📄 Media updates requested for digital product: ${id}`)
    }

    // Fetch the updated product
    const { data: updatedProducts } = await query.graph({
      entity: "digital_product",
      fields: [
        "id",
        "name", 
        "description",
        "product_variant_id",
        "medias.*",
        "product_variant.*",
        "product_variant.product.*",
        "created_at",
        "updated_at"
      ],
      filters: { id }
    })

    logger.info(`✅ Digital product updated successfully: ${updatedProducts[0].name}`)

    res.json({
      success: true,
      message: "Digital product updated successfully",
      digital_product: updatedProducts[0]
    })
  } catch (error) {
    logger.error(`❌ Error updating digital product ${id}:`, error)
    res.status(500).json({
      success: false,
      message: "Failed to update digital product",
      error: error.message
    })
  }
}
