<script lang="ts">
  import { _ } from '$lib/i18n'

  export let currentStep: number = 1

  const steps = [
    { number: 1, key: 'email', iconInactive: "/images/contInfoBeige.png", icon: "/images/contInfoBlue.png" },
    { number: 2, key: 'billing', iconInactive: '/images/billingAddressBeige.png', icon: '/images/billingAddressBlue.png' },
    { number: 3, key: 'payment', iconInactive: '/images/stepPaymentBeige.png', icon: '/images/stepPaymentBlue.png' }
  ]

</script>

<div class="header-div flex justify-between py-0 px-2">
    {#each steps as step, index (step.number)}
      <div class="px-2 flex items-center {index < steps.length - 1 ? 'flex-1' : ''}">
        <!-- Step Icon -->
        <div class="flex items-center justify-center w-10 h-10 rounded-full transition-all duration-300">
            <img class="w-auto h-8" src={step.number <= currentStep ? step.icon : step.iconInactive} alt="icon"/>
        </div>

        <!-- Step Label -->
        <div class="m-1 min-w-0 flex-1 px-1">
          <p class="text-lg transition-colors duration-300
            { step.number === currentStep
              ? 'font-aditional'
              : 'font-body'}"
              style="color: {step.number <= currentStep ? '#62AEC8' : 'var(--color-text-primary)'};"
          >
            {$_(`checkout.step_${step.key}`)}
          </p>
        </div>

        <!-- Connector Line -->
        {#if index < steps.length - 1}
          <div class="flex-1 mx-1">
            <div class="h-0.5 transition-colors duration-300"
              style="background: {step.number < currentStep ? '#62AEC8' : 'var(--color-border)'};"
            ></div>
          </div>
        {/if}
      </div>
    {/each}
</div>

<style>
  /* Responsive adjustments for mobile */
  @media (max-width: 640px) {
    .flex-1 {
      min-width: 0;
    }
  }
</style>
