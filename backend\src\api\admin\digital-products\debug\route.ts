import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@medusajs/framework/http"
import {
  ContainerRegistrationKeys,
} from "@medusajs/framework/utils"

// GET /admin/digital-products/debug - Debug digital product links
export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY)
  const logger = req.scope.resolve(ContainerRegistrationKeys.LOGGER)

  try {
    logger.info('🔍 Debug: Fetching digital products with linked variants')

    // Get all digital products
    const { data: digitalProducts } = await query.graph({
      entity: "digital_product",
      fields: [
        "id",
        "name", 
        "created_at",
        "updated_at"
      ]
    })

    // Get all product variants
    const { data: productVariants } = await query.graph({
      entity: "product_variant",
      fields: [
        "id",
        "title",
        "sku",
        "product_id",
        "product.*"
      ]
    })

    // Try to get linked data (this might work in some cases)
    let linkedData: any[] = []
    try {
      const { data: linkedProducts } = await query.graph({
        entity: "digital_product",
        fields: [
          "id",
          "name",
          "product_variant.*",
          "product_variant.product.*"
        ]
      })
      linkedData = linkedProducts || []
    } catch (linkError: any) {
      logger.warn('Could not fetch linked data directly')
    }

    // Create a summary
    const summary = {
      digital_products_count: digitalProducts?.length || 0,
      product_variants_count: productVariants?.length || 0,
      linked_products_count: linkedData.length,
      digital_products: digitalProducts || [],
      product_variants: productVariants || [],
      linked_data: linkedData,
      debug_info: {
        timestamp: new Date().toISOString(),
        note: "If you see duplicate link errors, check if any digital products are linked to the same variant"
      }
    }

    logger.info(`✅ Debug complete: ${summary.digital_products_count} digital products, ${summary.product_variants_count} variants`)

    res.json({
      success: true,
      summary
    })
  } catch (error) {
    logger.error('❌ Error in debug endpoint:', error)
    res.status(500).json({
      success: false,
      message: "Failed to fetch debug information",
      error: error.message
    })
  }
}
