import crypto from "crypto"

/**
 * AllPay utility functions for signature generation and API interactions
 */

export interface AllPayItem {
  name: string
  qty: number
  price: number
  vat: number
}

export interface AllPayPaymentRequest {
  login: string
  order_id: string
  items: AllPayItem[]
  currency?: string
  lang?: string
  notifications_url?: string
  success_url?: string
  backlink_url?: string
  client_name?: string
  client_email?: string
  client_phone?: string
  client_tehudat?: string
  add_field_1?: string
  add_field_2?: string
  expire?: number
}

export interface AllPayStatusRequest {
  login: string
  order_id: string
}

export interface AllPayRefundRequest {
  login: string
  order_id: string
  amount: number
  items?: Array<{ amount: string }>
}

/**
 * Generate SHA256 signature for AllPay API requests
 * Following AllPay's signature generation algorithm:
 * 1. Remove the 'sign' parameter from the request
 * 2. Exclude all parameters with empty values
 * 3. Sort the remaining keys in alphabetical order
 * 4. From the sorted list, take only the parameter values and join them into a single string using a colon (:) as the separator
 * 5. Append your API key to the end of the string, preceded by a colon
 * 6. Apply the SHA256 algorithm to the final string
 */
export function generateAllPaySignature(params: Record<string, any>, apiKey: string): string {
  // Remove sign parameter if it exists
  const { sign, ...paramsWithoutSign } = params

  // Sort keys alphabetically
  const sortedKeys = Object.keys(paramsWithoutSign).sort()
  const chunks: string[] = []

  sortedKeys.forEach((key) => {
    const value = paramsWithoutSign[key]

    if (Array.isArray(value)) {
      // Handle array values (like items)
      value.forEach((item) => {
        if (typeof item === 'object' && item !== null) {
          const sortedItemKeys = Object.keys(item).sort()
          sortedItemKeys.forEach((itemKey) => {
            const itemValue = item[itemKey]
            if (itemValue !== null && itemValue !== undefined && String(itemValue).trim() !== '') {
              chunks.push(String(itemValue).trim())
            }
          })
        }
      })
    } else {
      // Handle scalar values
      if (value !== null && value !== undefined && String(value).trim() !== '') {
        chunks.push(String(value).trim())
      }
    }
  })

  // Create signature string
  const signatureString = chunks.join(':') + ':' + apiKey
  
  // Generate SHA256 hash
  return crypto.createHash('sha256').update(signatureString).digest('hex')
}

/**
 * Verify AllPay webhook signature
 */
export function verifyAllPayWebhookSignature(data: Record<string, any>, apiKey: string): boolean {
  try {
    const expectedSignature = generateAllPaySignature(data, apiKey)
    return expectedSignature === data.sign
  } catch (error) {
    console.error('❌ AllPay webhook signature verification failed:', error)
    return false
  }
}

/**
 * Create AllPay payment request
 */
export async function createAllPayPayment(
  request: AllPayPaymentRequest,
  apiKey: string
): Promise<any> {
  try {
    const requestWithSign = {
      ...request,
      sign: generateAllPaySignature(request, apiKey)
    }

    console.log('🔄 Creating AllPay payment request:', {
      order_id: request.order_id,
      amount: request.items.reduce((sum, item) => sum + (item.price * item.qty), 0),
      currency: request.currency
    })

    const response = await fetch('https://allpay.to/app/?show=getpayment&mode=api8', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestWithSign),
    })

    if (!response.ok) {
      throw new Error(`AllPay API error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()

    if (!data.payment_url) {
      throw new Error(`AllPay API error: ${data.message || 'No payment URL returned'}`)
    }

    console.log('✅ AllPay payment request created successfully')
    return data
  } catch (error) {
    console.error('❌ AllPay payment request failed:', error)
    throw error
  }
}

/**
 * Check AllPay payment status
 */
export async function checkAllPayPaymentStatus(
  request: AllPayStatusRequest,
  apiKey: string
): Promise<any> {
  try {
    const requestWithSign = {
      ...request,
      sign: generateAllPaySignature(request, apiKey)
    }

    console.log('🔄 Checking AllPay payment status for order:', request.order_id)

    const response = await fetch('https://allpay.to/app/?show=paymentstatus&mode=api8', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestWithSign),
    })

    if (!response.ok) {
      throw new Error(`AllPay status API error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    console.log('✅ AllPay payment status retrieved:', data.status)
    return data
  } catch (error) {
    console.error('❌ AllPay status check failed:', error)
    throw error
  }
}

/**
 * Refund AllPay payment
 */
export async function refundAllPayPayment(
  request: AllPayRefundRequest,
  apiKey: string
): Promise<any> {
  try {
    const requestWithSign = {
      ...request,
      sign: generateAllPaySignature(request, apiKey)
    }

    console.log('🔄 Processing AllPay refund for order:', request.order_id)

    const response = await fetch('https://allpay.to/app/?show=refund&mode=api8', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestWithSign),
    })

    if (!response.ok) {
      throw new Error(`AllPay refund API error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()

    if (data.status !== 3 && data.status !== 4) { // 3 = refunded, 4 = partially refunded
      throw new Error(`AllPay refund failed: ${data.message || 'Unknown error'}`)
    }

    console.log('✅ AllPay refund processed successfully')
    return data
  } catch (error) {
    console.error('❌ AllPay refund failed:', error)
    throw error
  }
}

/**
 * Map AllPay payment status to human-readable format
 */
export function mapAllPayStatus(status: number): string {
  switch (status) {
    case 0:
      return 'unpaid'
    case 1:
      return 'paid'
    case 3:
      return 'refunded'
    case 4:
      return 'partially_refunded'
    default:
      return 'unknown'
  }
}

/**
 * Validate AllPay configuration
 */
export function validateAllPayConfig(config: { login?: string; api_key?: string }): void {
  if (!config.login) {
    throw new Error('AllPay login is required')
  }
  if (!config.api_key) {
    throw new Error('AllPay API key is required')
  }
}

/**
 * Format amount for AllPay (convert from cents to currency units)
 */
export function formatAllPayAmount(amountInCents: number): number {
  return Number((amountInCents / 100).toFixed(2))
}

/**
 * Create AllPay test card data for development
 */
export const ALLPAY_TEST_CARDS = {
  visa: {
    number: '****************',
    expiry: '12/25',
    cvv: '123',
    description: 'Visa test card (success)'
  },
  mastercard: {
    number: '****************',
    expiry: '12/25',
    cvv: '123',
    description: 'MasterCard test card (success)'
  },
  amex: {
    number: '***************',
    expiry: '12/25',
    cvv: '1234',
    description: 'AmEx test card (success)'
  },
  failure: {
    number: '****************',
    expiry: '12/25',
    cvv: '123',
    description: 'Test card (failure)'
  }
}
