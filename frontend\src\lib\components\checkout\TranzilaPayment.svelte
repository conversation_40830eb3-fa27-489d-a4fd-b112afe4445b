<script lang="ts">
  import { onMount } from 'svelte'
  import { _ } from '$lib/i18n'
  import { apiClient } from '$lib/api/client'
  import { cartActions } from '$lib/stores/cart'

  export let cart: any
  export let onComplete: (result: { success: boolean, order?: any, error?: string }) => void
  export let onBack: () => void

  let isProcessing = false
  let paymentError = ''
  let paymentUrl = ''
  let transactionId = ''
  let useIframe = true
  let iframeLoaded = false

  // Get Tranzila configuration from environment
  const tranzilaIframeMode = import.meta.env.VITE_TRANZILA_IFRAME_MODE === 'true'

  // Tranzila iframe configuration
  let iframeContainer: HTMLDivElement

  onMount(() => {
    useIframe = true; // tranzilaIframeMode
    console.log('🔍 Tranzila iframe mode:', tranzilaIframeMode, 'useIframe:', useIframe)
    console.log('🔍 Environment variable:', import.meta.env.VITE_TRANZILA_IFRAME_MODE)
    generatePaymentUrl()
  })

  async function generatePaymentUrl() {
    try {
      isProcessing = true
      paymentError = ''

      console.log('🔄 Initiating Tranzila payment session for cart:', cart)
      console.log('💰 Cart total:', cart.total_amount, cart.currency_code)
      console.log('🔍 Payment collection:', cart.payment_collection)

      // Use proper Medusa payment workflow
      if (!cart.payment_collection?.id) {
        throw new Error('No payment collection found. Please refresh and try again.')
      }

      // Create payment session using Medusa's payment workflow
      const paymentSession = await apiClient.initiatePaymentSession(cart, {
        provider_id: 'pp_tranzila_tranzila'
      })

      console.log('✅ Payment session created:', paymentSession)

      // Extract payment URL from session data
      const paymentCollection = paymentSession.data?.payment_collection
      if (!paymentCollection?.payment_sessions?.length) {
        throw new Error('No payment sessions found')
      }

      // Find the Tranzila payment session
      const tranzilaSession = paymentCollection.payment_sessions.find(
        (session: any) => session.provider_id === 'pp_tranzila_tranzila'
      )

      if (!tranzilaSession) {
        throw new Error('Tranzila payment session not found')
      }

      const sessionData = tranzilaSession.data
      if (!sessionData) {
        throw new Error('Invalid payment session data')
      }

      // Use the payment URL generated by the backend
      if (useIframe) {
        paymentUrl = sessionData.iframe_url || sessionData.payment_url
      } else {
        paymentUrl = sessionData.redirect_url || sessionData.payment_url
      }

      if (!paymentUrl) {
        throw new Error('No payment URL found in session data')
      }

      transactionId = sessionData.transaction_id || sessionData.id
      console.log('✅ Tranzila payment URL generated:', paymentUrl)

    } catch (error) {
      console.error('❌ Failed to initiate Tranzila payment:', error)
      paymentError = error instanceof Error ? error.message : $_('checkout.payment_failed')
    } finally {
      isProcessing = false
    }
  }

  function handleRedirectPayment() {
    if (!paymentUrl) {
      paymentError = $_('checkout.payment_url_not_available')
      return
    }

    console.log('🔄 Redirecting to Tranzila payment page')
    
    // Store cart ID and transaction ID in sessionStorage for return handling
    sessionStorage.setItem('tranzila_cart_id', cart.id)
    sessionStorage.setItem('tranzila_transaction_id', transactionId)
    
    // Redirect to Tranzila payment page
    window.location.href = paymentUrl
  }

  function handleIframeLoad() {
    iframeLoaded = true
    console.log('✅ Tranzila iframe loaded')
  }

  function handleIframeError() {
    console.error('❌ Tranzila iframe failed to load')
    paymentError = $_('checkout.iframe_load_failed')
    iframeLoaded = false
  }

  // Listen for messages from Tranzila iframe
  function handleMessage(event: MessageEvent) {
    if (!useIframe) return

    console.log('📩 Received message from Tranzila iframe:', event.data)

    // Verify origin for security - Tranzila domains only
    const allowedOrigins = ['https://direct.tranzila.com', 'https://secure5.tranzila.com']
    if (!allowedOrigins.includes(event.origin)) {
      console.warn('Received message from unauthorized origin:', event.origin)
      return
    }

    console.log('📨 Received message from Tranzila iframe:', event.data)

    try {
      // Tranzila iframe sends messages in different formats
      let data = event.data

      // Handle string data (URL encoded or JSON)
      if (typeof data === 'string') {
        if (data.startsWith('{')) {
          data = JSON.parse(data)
        } else {
          // Parse URL-encoded response
          const params = new URLSearchParams(data)
          data = Object.fromEntries(params.entries())
        }
      }

      console.log('📋 Parsed Tranzila response:', data)

      // Handle Tranzila's response format
      if (data.Response === '000' || data.response === '000') {
        // Success
        console.log('✅ Payment completed successfully:', data)
        handlePaymentComplete({
          success: true,
          transactionId: data.myid || data.MyID,
          responseCode: data.Response || data.response,
          confirmationCode: data.ConfirmationCode || data.confirmation_code,
          responseMessage: data.Responsemessage || data.response_message || 'Transaction approved',
          amount: data.sum || data.Sum,
          currency: data.currency || data.Currency
        })
      } else if ((data.Response && data.Response !== '000') || (data.response && data.response !== '000')) {
        // Error
        console.log('❌ Payment failed:', data)
        handlePaymentError({
          responseCode: data.Response || data.response,
          responseMessage: data.Responsemessage || data.response_message || 'Transaction failed'
        })
      }
    } catch (error) {
      console.error('Error parsing iframe message:', error)
    }
  }

  async function handlePaymentComplete(result: any) {
    try {
      console.log('✅ Tranzila payment completed:', result)

      // Validate the payment result
      if (!result.success || result.responseCode !== '000') {
        throw new Error(result.responseMessage || 'Payment was not successful')
      }

      // Store payment details for order completion
      const paymentDetails = {
        transaction_id: result.transactionId,
        confirmation_code: result.confirmationCode,
        response_code: result.responseCode,
        masked_card: result.maskedCardNumber,
        card_type: result.cardType
      }

      console.log('💳 Payment details:', paymentDetails)

      // Complete the cart
      console.log('🔄 Completing cart:', cart.id)
      const completeResponse = await apiClient.completeCart(cart.id)

      if (completeResponse.data?.type === 'order' && completeResponse.data.order) {
        // Order placed successfully
        console.log('✅ Order placed successfully:', completeResponse.data.order.id)
        cartActions.initialize() // Refresh cart
        onComplete({
          success: true,
          order: completeResponse.data.order
        })
      } else {
        console.error('❌ Cart completion failed:', completeResponse)
        const errorMsg = $_('checkout.order_completion_failed')
        onComplete({ success: false, error: errorMsg })
      }
    } catch (error) {
      console.error('❌ Error completing order:', error)
      const errorMsg = error instanceof Error ? error.message : $_('checkout.order_completion_failed')
      onComplete({ success: false, error: errorMsg })
    }
  }

  function handlePaymentError(error: any) {
    console.error('❌ Tranzila payment error:', error)
    const errorMessage = error.responseMessage || error.message || $_('checkout.payment_failed')
    paymentError = errorMessage
    onComplete({ success: false, error: errorMessage })
  }

  // Add event listener for iframe messages
  onMount(() => {
    if (useIframe) {
      window.addEventListener('message', handleMessage)
      /*return () => {
        window.removeEventListener('message', handleMessage)
      }*/
    }
  })
</script>

<svelte:head>
  <title>{$_('checkout.tranzila_payment')} - Hebrew Book Store</title>
</svelte:head>

<div class="tranzila-payment">
  {#if paymentError}
    <div class="error-message">
      <p>{paymentError}</p>
      <button type="button" on:click={generatePaymentUrl} class="retry-button">
        {$_('checkout.retry')}
      </button>
    </div>
  {:else if isProcessing}
    <div class="loading-state">
      <div class="spinner"></div>
      <p>{$_('checkout.preparing_payment')}</p>
    </div>
  {:else if useIframe && paymentUrl}
    <div class="iframe-container">
      
      {#if !iframeLoaded}
        <div class="iframe-loading">
          <div class="spinner"></div>
          <p>{$_('checkout.loading_payment_form')}</p>
        </div>
      {/if}
      
      <iframe
        src={paymentUrl}
        title="Tranzila Payment"
        class="payment-iframe"
        class:loaded={iframeLoaded}
        on:load={handleIframeLoad}
        on:error={handleIframeError}
        frameborder="0"
        scrolling="auto"
      ></iframe>
    </div>
  {/if}

  <div class="payment-actions">
    <button type="button" on:click={onBack} class="back-button">
      {$_('checkout.back')}
    </button>
  </div>
</div>

<style>
  .tranzila-payment {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem;
  }

  .error-message {
    background: var(--error-bg);
    border: 1px solid var(--error-border);
    color: var(--error-color);
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    text-align: center;
  }

  .retry-button {
    background: var(--error-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 0.5rem;
  }

  .loading-state {
    text-align: center;
    padding: 2rem;
  }

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .iframe-container {
    margin-bottom: 2rem;
  }

  .iframe-loading {
    text-align: center;
    padding: 2rem;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
  }

  .payment-iframe {
    width: 100%;
    min-height: 600px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .payment-iframe.loaded {
    opacity: 1;
  }

  .payment-actions {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
  }

  .back-button {
    background: var(--secondary-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }

  .back-button:hover {
    background: var(--hover-bg);
  }
</style>
