<script lang="ts">
  import { _ } from '$lib/i18n'

  export let email: string = ''
  export let isLoading: boolean = false
  export let onSubmit: (email: string) => void

  let emailInput = email
  let emailError = ''

  function validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  function handleSubmit() {
    emailError = ''
    
    if (!emailInput.trim()) {
      emailError = $_('checkout.email_required')
      return
    }

    if (!validateEmail(emailInput.trim())) {
      emailError = $_('checkout.email_invalid')
      return
    }

    onSubmit(emailInput.trim())
  }

  function handleKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      handleSubmit()
    }
  }
</script>

<div class="card-classic p-6">
  <div class="mb-6">
    <p class="font-book-text" style="color: var(--color-text-primary);">
      {$_('checkout.email_description')}
    </p>
  </div>

  <form on:submit|preventDefault={handleSubmit} class="space-y-4">
    <div>
      <label for="email" class="label-base">
        {$_('checkout.email_address')} *
      </label>
      <input
        id="email"
        type="email"
        bind:value={emailInput}
        on:keypress={handleKeyPress}
        disabled={isLoading}
        class="input-base transition-colors duration-200 "
        class:border-red-500={emailError}
        class:opacity-50={isLoading}
        placeholder={$_('checkout.email_placeholder')}
        required
      />
      {#if emailError}
        <p class="mt-1 text-sm text-red-600 font-book-text">{emailError}</p>
      {/if}
    </div>

    <div class="flex justify-end pt-4">
      <button
        type="submit"
        disabled={isLoading || !emailInput.trim()}
        class="btn-classic"
        class:opacity-50={isLoading || !emailInput.trim()}
      >
        {#if isLoading}
          <div class="flex items-center">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            {$_('checkout.processing')}
          </div>
        {:else}
          {$_('checkout.continue')} →
        {/if}
      </button>
    </div>
  </form>
</div>
