<script lang="ts">
	import { onMount } from 'svelte'
	import { _ } from '$lib/i18n'
	import { getCurrentLocale, isRTL } from '$lib/i18n'
	import { productsStore, productActions, isLoadingProducts } from '$lib/stores/products'
	import { formatPriceWithCurrency, currentCurrency, currentRegion } from '$lib/stores/currency'
	import { chaptersStore } from '$lib/stores/chapters'
	import { cartActions, cartLoading } from '$lib/stores/cart'
	import type { Product } from '$lib/types/products'
	import type { Chapter } from '$lib/stores/chapters'
	import { apiClient } from '$lib/api/client'
    import { goto } from '$app/navigation';

	let currentLocale = 'en'
	let rtl = false
	let selectedProduct: Product | null = null
	let expandedChapter: string | null = null
	let videoUrl = '' // Default video for about section
	let heroVideoUrl = '' // Hero video URL from store settings
	let aboutContent = '' // About content from store settings
	let imageLoading = true
	let imageError = false
	let addToCartSuccess = false
	let callToAction = false

	$: currentLocale = getCurrentLocale()
	$: rtl = isRTL(currentLocale)
	$: allProducts = $productsStore
	$: isLoading = $isLoadingProducts
	$: currency = $currentCurrency
	$: region = $currentRegion
	$: chapters = $chaptersStore.chapters
	$: isLoadingChapters = $chaptersStore.isLoading

	// Filter products to show only "Book" type on main page
	// Admin can control what appears by setting product type to "Book" in MedusaJS admin panel
	$: bookProducts = allProducts.filter(product => {
		const productType = product.type?.toLowerCase()
		return productType === 'book'
	})

	// Reload products when region changes (for updated pricing)
	$: if (region) {
		console.log('🏠 Main page: Region changed to', region.name, 'Currency:', currency)
		console.log('🏠 Main page: Products will reload automatically for updated pricing')
	}

	// Log currency changes
	$: if (currency) {
		console.log('💰 Main page: Currency updated to', currency)
	}

	// Add product to cart
	async function addToCart(product: Product) {
		if (!product) {
			console.warn('⚠️ No product selected to add to cart')
			return
		}

		try {
			await cartActions.addItem(product, 1)
			console.log('✅ Added to cart:', product.title)

			// Show success feedback
			addToCartSuccess = true
			setTimeout(() => {
				addToCartSuccess = false
			}, 2000) // Hide success message after 2 seconds

		} catch (error) {
			console.error('❌ Failed to add to cart:', error)
			// Optional: Show error feedback to user
		}
	}

	// Handle start learning button click
	async function handleStartLearning() {
		if (selectedProduct) {
			await addToCart(selectedProduct)
		} else {
			console.warn('⚠️ No product selected for start learning')
		}
	}

	onMount(async () => {
		// Load products from MedusaJS
		console.log('🏠 Loading products for main page...')
		await productActions.loadProducts()

		// Fetch video URLs from store settings (custom endpoint)
		try {
			console.log('Attempting to fetch store settings...')
			const response = await apiClient.getStoreSettings()

			if (response.data && response.data.settings) {
				console.log('Store settings:', response.data)
				const settings = response.data.settings
				if (settings?.main_video_url) {
					videoUrl = settings.main_video_url
					console.log('About section video URL from settings:', videoUrl)
				}
				if (settings?.hero_video_url) {
					heroVideoUrl = settings.hero_video_url
					console.log('Hero video URL from settings:', heroVideoUrl)
				}
				if (settings?.about_content) {
					aboutContent = settings.about_content
				}
			} else {
				console.log('No store settings or error:', response.error)
			}
		} catch (error) {
			console.log('Error fetching store settings:', error)
			console.log('Using default video URLs')
		}
	})

	// Select first book when bookProducts are loaded
	$: if (bookProducts.length > 0 && !selectedProduct) {
		selectProduct(bookProducts[0])
	}

	function selectProduct(product: Product) {
		selectedProduct = product
		expandedChapter = null
		// Reset image states when selecting a new product
		imageLoading = true
		imageError = false
		console.log('📖 Selected product:', product.title)
		if (product.cover_image) {
			console.log('🖼️ Product has cover image:', product.cover_image)
		} else {
			console.log('📖 Product has no cover image, using default icon')
		}

		// Load chapters for this book
		chaptersStore.loadChaptersForBook(product)
	}

	function toggleChapterPreview(chapterId: string) {
		expandedChapter = expandedChapter === chapterId ? null : chapterId
	}


</script>

<svelte:head>
	<title>{$_('home.title')} - Hebrew Book Store</title>
	<meta name="description" content={$_('home.description')} />
</svelte:head>

<div class="min-h-screen" style="background: var(--color-bg-primary);" class:rtl>
	<!-- Hero Video Section -->
	{#if heroVideoUrl}
		<section id="hero" class="flex justify-center items-end overflow-hidden max-h-full ">
			<!-- Video Background -->
			<div class="relative">
				<video autoplay muted loop
					src="{heroVideoUrl}?autoplay=1&mute=1&loop=1&controls=0&showinfo=0&rel=0&modestbranding=1&playsinline=1"
					title="Hero Video"
				></video>
			</div>

			<!-- Hero Content -->
			<div class="absolute z-10 flex items-end justify-end text-center mb-6 px-4 py-4">
				<div class="max-w-6xl mx-auto">
					<h1 class="font-title text-2xl md:text-6xl font-bold drop-shadow-lg mb-6"
					style="color: var(--color-background);">
						{$_('hero.title')}
					</h1>
					<button
						class="btn-hero "
						on:click={() => goto("/catalog")}
					>
						{$_('hero.cta_button')}
					</button>
				</div>
			</div>

			<!-- Scroll indicator
			<div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
				<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
				</svg>
			</div> -->
		</section>
	{/if}

	<!-- About Us Section -->
	<section id="about" class="py-16 px-4 sm:px-6 lg:px-8">
		<div class="max-w-7xl mx-auto">
			<div class="grid grid-cols-1 lg:grid-cols-12 gap-8">

				<!-- Left: Title -->
				<div class="lg:col-span-3 flex flex-col items-end justify-start ">
					<h1 class="font-title text-4xl md:text-5xl lg:text-6xl font-medium text-center lg:text-left"
						style="color: var(--color-text-primary);">
						{$_('about.title')}
					</h1>
				</div>

				<!-- Center: Video Section -->
				<div class="lg:col-span-6">
					<div class="relative">
							<div class="decorative-border overflow-hidden" style="border-color: var(--color-border);">
								<div class="aspect-w-16 aspect-h-9">
									<iframe
										src={videoUrl}
										title={$_('about.video_title')}
										frameborder="0"
										allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
										allowfullscreen
										class="w-full h-full"
										style="aspect-ratio: 16/9; min-height: 300px;"
									></iframe>
								</div>
							
						</div>
					</div>
				</div>

				<!-- Right: Description -->
				<div class="lg:col-span-3 flex items-top">
					<div class="space-y-4">
						<p class="font-body text-xl leading-relaxed"
							style="color: var(--color-text-secondary);">
							
							{$_('about.description_1')}
						</p>
						<p class="font-body text-xl leading-relaxed"
							style="color: var(--color-text-secondary);">
							{$_('about.description_2')}
						</p>
					</div>
				</div>

			</div>
		</div>
	</section>

	<!-- Books Section -->
	<section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">

		<!-- Loading State -->
		{#if isLoading}
			<div class="text-center mb-12">
				<div class="inline-flex items-center px-4 py-2 font-book-text text-sm" style="color: var(--color-text-secondary);">
					<div class="animate-spin rounded-full h-4 w-4 border-b-2 mr-3" ></div>
					Loading books...
				</div>
			</div>
		{:else if bookProducts.length === 0}
			<div class="text-center mb-12">
				<p class="font-book-text text-base" style="color: var(--color-text-secondary);">
					{$_('home.no_products')}
				</p>
			</div>
		{:else}
			<!-- Book Selection Tabs -->
			<div class="flex justify-center mb-2">
				<div class="flex gap-2 flex-wrap">
					{#each bookProducts as product}
						<button
							class="font-book-text px-6 py-3 text-base font-medium rounded transition-all duration-300 {selectedProduct?.id === product.id ? 'btn-classic' : 'hover:bg-opacity-70'}"
							style="{selectedProduct?.id === product.id ? '' : `color: var(--color-text-accent); background: var(--color-bg-subtle);`}"
							on:click={() => selectProduct(product)}
						>
							{product.title}
						</button>
					{/each}
				</div>
			</div>
		{/if}

		{#if selectedProduct}
			<!-- Selected Book Details -->
			<div class="card-classic overflow-hidden mb-2 " style="background: var(--color-bg-primary); border-style: hidden;">
				<div class="md:flex">
					<!-- Book Cover -->
					<div class="md:w-1/3 p-8 flex items-center justify-center">
						<div class="text-center">
							<div class="w-auto h-auto m-6 flex items-center justify-center overflow-hidden relative" style="background: var(--color-bg-primary);">
								{#if selectedProduct.cover_image && !imageError}
									<!-- Product Image -->
									<img
										src={selectedProduct.cover_image}
										alt={selectedProduct.title}
										class="w-full h-full object-cover transition-opacity duration-300"
										class:opacity-0={imageLoading}
										class:opacity-100={!imageLoading}
										loading="lazy"
										on:load={() => {
											imageLoading = false
											imageError = false
											console.log('✅ Product image loaded successfully')
										}}
										on:error={() => {
											console.warn('❌ Failed to load product image:', selectedProduct?.cover_image)
											imageLoading = false
											imageError = true
										}}
									/>

									<!-- Loading indicator (only show while loading) -->
									{#if imageLoading}
										<div class="absolute inset-0 flex items-center justify-center animate-pulse" style="background: var(--color-bg-subtle);">
											<div class="text-center" style="color: var(--color-text-light);">
												<div class="w-6 h-6 border-2 border-neutral-300 border-t-accent rounded-full animate-spin mx-auto mb-2"></div>
												<div class="font-body text-xs">Loading image...</div>
											</div>
										</div>
									{/if}
								{/if}

								{#if !selectedProduct.cover_image || imageError}
									<!-- Default book icon when no image or image failed -->
									<div class="text-center" style="color: var(--color-text-primary);">
										<div class="font-title text-4xl mb-2">📖</div>
										<div class="font-title text-sm font-medium px-2">{selectedProduct.title}</div>
										{#if imageError}
											<div class="font-book-text text-xs mt-1" style="color: var(--color-text-light);">Image unavailable</div>
										{/if}
									</div>
								{/if}
							</div>
							<p class="font-book-text text-base" style="color: var(--color-text-secondary);">{selectedProduct.short_description}</p>
						</div>
					</div>

					<!-- Book Info -->
					<div class="md:w-2/3 p-8 justify-between">
						<h1 class="font-title-blue text-4xl mb-2" >{selectedProduct.title}</h1>
						<!-- Description -->
						<p class="font-body text-base leading-relaxed mb-8" style="color: var(--color-text-secondary);">{selectedProduct.description}</p>
				
						<div class="flex flex-col sm:flex-row gap-4 align-bottom items-bottom justify-bottom">
							<button class="decorative-border px-6 py-3 font-body font-medium transition-all duration-300 hover:shadow-md" style="background: var(--color-bg-subtle); color: var(--color-text-accent); border-color: var(--color-border);">
								{$_('home.preview_chapters')}
							</button>
						</div>
					</div>
				</div>
			</div>
		{/if}
	</section>

	<!-- Interactive Chapter Table of Contents -->
	{#if selectedProduct && chapters.length > 0}
		<section class="py-16" style="background: var(--color-bg-accent);">
			<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div class="text-center mb-12">
					<h2 class="font-title text-3xl md:text-4xl font-medium mb-4" style="color: var(--color-text-primary);">
						{$_('home.chapters_title')}
					</h2>
					<h3 class="font-title text-lg md:text-xl font-medium mb-6 book-title">
						{$_('home.chapters_subtitle')} - {selectedProduct.title}
					</h3>
					<p class="font-body text-base leading-relaxed" style="color: var(--color-text-secondary);">
						{$_('home.chapters_description')}
					</p>
				</div>

				{#if isLoadingChapters}
					<div class="text-center py-8">
						<div class="inline-flex items-center px-4 py-2 font-body text-sm" style="color: var(--color-text-secondary);">
							<div class="animate-spin rounded-full h-4 w-4 border-b-2 mr-3" style="border-color: var(--color-text-accent);"></div>
							Loading chapters...
						</div>
					</div>
				{:else if chapters.length === 0}
					<div class="text-center py-8">
						<p class="font-body text-base" style="color: var(--color-text-secondary);">
							No chapters available for this book.
						</p>
					</div>
				{:else}
					<div class="space-y-4">
						{#each chapters as chapter}
						<div class="card-classic overflow-hidden">
							<!-- Chapter Header -->
							<button
								class="w-full px-6 py-4 text-left transition-all duration-300 hover:shadow-md focus:outline-none"
								style="background: var(--color-bg-primary);"
								on:click={() => toggleChapterPreview(chapter.id)}
							>
								<div class="flex items-center justify-between">
									<div class="flex items-center space-x-4">
										<div class="flex-shrink-0">
											<div class="decorative-border w-10 h-10 flex items-center justify-center" style="background: var(--color-text-accent); border-color: var(--color-border);">
												<span class="font-title text-sm font-medium text-white">
													{chapter.chapter_order || 1}
												</span>
											</div>
										</div>
										<div>
											<h3 class="font-title text-lg font-medium mb-1 book-title">{chapter.title}</h3>
											<p class="font-body text-sm" style="color: var(--color-text-secondary);">{chapter.description}</p>
										</div>
									</div>
									<div class="flex items-center space-x-4">
										<div class="text-right">
											<div class="flex items-center space-x-2 mb-1">
												{#if chapter.is_free}
													<div class="decorative-border px-2 py-1" style="background: var(--color-text-accent); border-color: var(--color-border);">
														<span class="font-body text-xs font-medium uppercase tracking-wide text-white">
															{$_('home.free')}
														</span>
													</div>
												{/if}
											</div>
											<div class="font-body text-xs" style="color: var(--color-text-secondary);">{chapter.chapter_duration || `${chapter.reading_time_minutes} min`}</div>
										</div>
										<div class="w-6 h-6 rounded-full flex items-center justify-center transition-all duration-300 {expandedChapter === chapter.id ? 'rotate-180' : ''}" style="background: var(--color-text-accent);">
											<svg
												class="w-3 h-3 text-white"
												fill="none"
												stroke="currentColor"
												viewBox="0 0 24 24"
											>
												<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
											</svg>
										</div>
									</div>
								</div>
							</button>

							<!-- Chapter Preview Content -->
							{#if expandedChapter === chapter.id}
								<div class="px-6 pb-6" style="border-top: 1px solid var(--color-border-light);">
									<div class="pt-4">
										<h4 class="font-book-title text-base font-bold mb-3" style="color: var(--color-text-primary);">{$_('home.chapter_preview')}</h4>
										<div class="decorative-border p-4 mb-4" style="background: var(--color-bg-subtle); border-color: var(--color-border);">
											<p class="font-book-text text-sm leading-relaxed" style="color: var(--color-text-secondary);">{chapter.preview_content}</p>
										</div>

										<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
											<div class="flex flex-wrap items-center gap-3 font-book-text text-xs" style="color: var(--color-text-accent);">
												<span class="flex items-center">
													<span class="w-2 h-2 rounded-full mr-2" style="background: var(--color-border-accent);"></span>
													{$_('home.chapter')} {chapter.chapter_order || 1}
												</span>
												<span class="flex items-center">
													<span class="w-2 h-2 rounded-full mr-2" style="background: var(--color-border-accent);"></span>
													{chapter.chapter_duration || `${chapter.reading_time_minutes} min`}
												</span>
											</div>

											<div class="flex gap-2">
												{#if chapter.is_free}
													<button class="btn-classic text-sm px-4 py-2">
														{$_('home.start_free_chapter')}
													</button>
												{:else}
													<button class="decorative-border px-4 py-2 font-book-text text-sm font-medium transition-all duration-300 hover:shadow-md" style="background: var(--color-bg-subtle); color: var(--color-text-accent); border-color: var(--color-border);">
														{$_('home.preview')}
													</button>
													<button class="btn-classic text-sm px-4 py-2">
														{$_('home.unlock_chapter')}
													</button>
												{/if}
											</div>
										</div>
									</div>
								</div>
							{/if}
						</div>
					{/each}
				</div>
			{/if}

				<!-- Call to Action -->
			{#if callToAction}
				<div class="text-center mt-12">
					<div class="card-classic p-8 max-w-2xl mx-auto">
						<h3 class="font-book-title text-2xl font-bold mb-2" style="color: var(--color-text-primary);">{$_('home.cta_hebrew')}</h3>
						<h4 class="font-book-title text-xl font-medium mb-4" style="color: var(--color-text-accent);">{$_('home.cta_title')}</h4>
						<p class="font-book-text text-base leading-relaxed mb-6 max-w-xl mx-auto" style="color: var(--color-text-secondary);">
							{$_('home.cta_description')}
						</p>
						<div class="flex flex-col sm:flex-row gap-4 justify-center">
							<button
								class="btn-classic px-8 py-3"
								on:click={handleStartLearning}
								disabled={$cartLoading || !selectedProduct}
								class:opacity-50={$cartLoading}
								class:bg-green-600={addToCartSuccess}
								class:hover:bg-green-700={addToCartSuccess}
							>
								{#if $cartLoading}
									<div class="inline-flex items-center">
										<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
										{$_('home.adding_to_cart')}
									</div>
								{:else if addToCartSuccess}
									<div class="inline-flex items-center">
										<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
										</svg>
										{$_('cart.item_added')}
									</div>
								{:else}
									{$_('home.start_learning')} - {selectedProduct ? (selectedProduct.formatted_price || formatPriceWithCurrency(selectedProduct.price)) : ''}
								{/if}
							</button>
							<button class="decorative-border px-8 py-3 font-book-text font-medium transition-all duration-300 hover:shadow-md" style="background: var(--color-bg-subtle); color: var(--color-text-accent); border-color: var(--color-border);">
								{$_('home.try_free')}
							</button>
						</div>
					</div>
				</div>
				{/if}
			</div>
		</section>
	{/if}
</div>

<style>
	.hero-video-section {
		position: relative;
		width: 100vw;
		height: 100vh;
		overflow: hidden;
		background: transparent;
	}

	.hero-iframe {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		border: none;
		outline: none;
		padding: 0%;
		margin: 0%;
		pointer-events: none;
		background-color: var(--color-bg-primary);
	}



	/* Mobile optimizations */
	@media (max-width: 768px) {
		.hero-video-section h1 {
			font-size: 2.5rem;
		}
	}
</style>
