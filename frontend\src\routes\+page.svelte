<script lang="ts">
	import { onMount } from "svelte";
	import { _ } from "$lib/i18n";
	import { getCurrentLocale, isRTL } from "$lib/i18n";
	import { goto } from "$app/navigation";
	import {
		productsStore,
		productActions,
		isLoadingProducts,
	} from "$lib/stores/products";
	import {
		formatPriceWithCurrency,
		currentCurrency,
		currentRegion,
	} from "$lib/stores/currency";
	import { cartActions, cartLoading } from "$lib/stores/cart";
	import type { Product } from "$lib/types/products";
	import { apiClient } from "$lib/api/client";

	let currentLocale = "en";
	let rtl = false;
	let videoUrl = ""; // Default video for about section
	let heroVideoUrl = ""; // Hero video URL from store settings
	let aboutContent = ""; // About content from store settings
	let addToCartSuccess = false;
	let showScrollIndicator = true;
	let currentSection = 0;
	let sections: HTMLElement[] = [];
	let scrollY = 0;
	let innerHeight = 0;

	$: currentLocale = getCurrentLocale();
	$: rtl = isRTL(currentLocale);
	$: allProducts = $productsStore;
	$: isLoading = $isLoadingProducts;
	$: currency = $currentCurrency;
	$: region = $currentRegion;

	// Filter products to show only "Book" type on main page
	// Admin can control what appears by setting product type to "Book" in MedusaJS admin panel
	$: bookProducts = allProducts.filter((product) => {
		const productType = product.type?.toLowerCase();
		return productType === "book";
	});

	// Reload products when region changes (for updated pricing)
	$: if (region) {
		console.log(
			"🏠 Main page: Region changed to",
			region.name,
			"Currency:",
			currency,
		);
		console.log(
			"🏠 Main page: Products will reload automatically for updated pricing",
		);
	}

	// Log currency changes
	$: if (currency) {
		console.log("💰 Main page: Currency updated to", currency);
	}

	// Add product to cart
	async function addToCart(product: Product) {
		if (!product) {
			console.warn("⚠️ No product selected to add to cart");
			return;
		}

		try {
			await cartActions.addItem(product, 1);
			console.log("✅ Added to cart:", product.title);

			// Show success feedback
			addToCartSuccess = true;
			setTimeout(() => {
				addToCartSuccess = false;
			}, 2000); // Hide success message after 2 seconds
		} catch (error) {
			console.error("❌ Failed to add to cart:", error);
			// Optional: Show error feedback to user
		}
	}

	onMount(async () => {
		// Load products from MedusaJS
		console.log("🏠 Loading products for main page...");
		await productActions.loadProducts();

		// Fetch video URLs from store settings (custom endpoint)
		try {
			console.log("Attempting to fetch store settings...");
			const response = await apiClient.getStoreSettings();

			if (response.data && response.data.settings) {
				console.log("Store settings:", response.data);
				const settings = response.data.settings;
				if (settings?.main_video_url) {
					videoUrl = settings.main_video_url;
					console.log(
						"About section video URL from settings:",
						videoUrl,
					);
				}
				if (settings?.hero_video_url) {
					heroVideoUrl = settings.hero_video_url;
					console.log("Hero video URL from settings:", heroVideoUrl);
				}
				if (settings?.about_content) {
					aboutContent = settings.about_content;
				}
			} else {
				console.log("No store settings or error:", response.error);
			}
		} catch (error) {
			console.log("Error fetching store settings:", error);
			console.log("Using default video URLs");
		}

		// Initialize scroll functionality
		initializeScrollFeatures();
	});

	function initializeScrollFeatures() {
		// Wait for DOM to be fully rendered
		setTimeout(() => {
			// Get all main sections
			sections = [
				document.getElementById('hero'),
				document.getElementById('about'),
				...document.querySelectorAll('.book-screen-container')
			].filter(Boolean) as HTMLElement[];

			// Initialize intersection observer for scroll animations
			initializeScrollAnimations();

			// Handle scroll events for indicator visibility and parallax
			const handleScroll = () => {
				const scrollTop = window.pageYOffset;
				const windowHeight = window.innerHeight;
				const documentHeight = document.documentElement.scrollHeight;

				// Update scroll position for reactive statements
				scrollY = scrollTop;
				innerHeight = windowHeight;

				// Hide scroll indicator when near bottom or when scrolled significantly
				showScrollIndicator = scrollTop < documentHeight * 0.3 &&
									 scrollTop < documentHeight - windowHeight;

				// Apply parallax effects
				applyParallaxEffects(scrollTop);
			};

			window.addEventListener('scroll', handleScroll);

			// Initial check
			handleScroll();

			// Cleanup function
			return () => {
				window.removeEventListener('scroll', handleScroll);
			};
		}, 100);
	}

	function initializeScrollAnimations() {
		// Create intersection observer for fade-in animations
		const observerOptions = {
			threshold: 0.1,
			rootMargin: '0px 0px -50px 0px'
		};

		const observer = new IntersectionObserver((entries) => {
			entries.forEach(entry => {
				if (entry.isIntersecting) {
					entry.target.classList.add('animate-in');
				}
			});
		}, observerOptions);

		// Observe all animatable elements
		const animatableElements = document.querySelectorAll('.animate-on-scroll');
		animatableElements.forEach(el => observer.observe(el));

		// Observe book cards for staggered animation
		const bookCards = document.querySelectorAll('.book-card');
		bookCards.forEach((card, index) => {
			(card as HTMLElement).style.transitionDelay = `${index * 0.2}s`;
			observer.observe(card);
		});
	}

	function applyParallaxEffects(scrollTop: number) {
		// Hero video parallax
		const heroVideo = document.getElementById('myVideo');
		if (heroVideo) {
			const parallaxSpeed = 0.5;
			heroVideo.style.transform = `translateY(${scrollTop * parallaxSpeed}px)`;
		}

		// About section elements parallax
		const aboutTitle = document.querySelector('#about h1');
		if (aboutTitle) {
			const parallaxSpeed = 0.3;
			(aboutTitle as HTMLElement).style.transform = `translateY(${scrollTop * parallaxSpeed}px)`;
		}

		// Book images parallax
		const bookImages = document.querySelectorAll('.book-image');
		bookImages.forEach((img, index) => {
			const parallaxSpeed = 0.2 + (index * 0.1);
			(img as HTMLElement).style.transform = `translateY(${scrollTop * parallaxSpeed}px)`;
		});
	}

	function scrollToNextSection() {
		const currentScrollTop = window.pageYOffset;

		// Find the next section to scroll to
		for (let i = 0; i < sections.length; i++) {
			const section = sections[i];
			const sectionTop = section.offsetTop;

			if (sectionTop > currentScrollTop + 150) {
				section.scrollIntoView({
					behavior: 'smooth',
					block: 'start'
				});
				break;
			}
		}
	}
</script>

<svelte:head>
	<title>{$_("home.title")} - Hebrew Book Store</title>
	<meta name="description" content={$_("home.description")} />
</svelte:head>

<svelte:window bind:scrollY bind:innerHeight />

<div
	class="min-h-screen"
	style="background: var(--color-bg-primary);"
	class:rtl
>
	<!-- Hero Video Section -->
	{#if heroVideoUrl}
		<section id="hero" class="hero-video-container">
			<div class="video-wrapper">
				<!-- Video -->
				<video
					autoplay
					muted
					loop
					src={heroVideoUrl}
					id="myVideo"
				></video>
			</div>
			<!-- Overlay -->
			<div class="hero-overlay">
				<div class="hero-content">
					<h1
						class="font-title text-2xl md:text-4xl lg:text-7xl font-bold mb-8"
						style="color: var(--color-background);"
					>
						{$_("hero.title")}
					</h1>
					<button class="btn-hero" on:click={() => goto("/catalog")}>
						{$_("hero.cta_button")}
					</button>
				</div>
			</div>
		</section>
	{/if}

	<!-- About Us Section -->
	<section id="about" class="about-section pt-32 animate-on-scroll">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full">
			<div class="grid grid-cols-1 lg:grid-cols-12 gap-8 h-full ">
				<!-- Left: Title -->
				<div
					class="lg:col-span-3 flex flex-col items-end justify-start animate-on-scroll"
				>
					<h1
						class="font-title text-4xl md:text-5xl lg:text-6xl font-medium text-center lg:text-left"
						style="color: var(--color-text-primary);"
					>
						{$_("about.title")}
					</h1>
				</div>

				<!-- Center: Video Section -->
				<div class="lg:col-span-6 flex items-start justify-start animate-on-scroll">
					<div class="relative w-full">
						<div
							class="decorative-border overflow-hidden"
							style="border-color: var(--color-border);"
						>
							<div class="aspect-w-16 aspect-h-9">
								<iframe
									src={videoUrl}
									title={$_("about.video_title")}
									frameborder="0"
									allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
									allowfullscreen
									class="w-full h-full"
									style="aspect-ratio: 16/9; min-height: 300px;"
								></iframe>
							</div>
						</div>
					</div>
				</div>

				<!-- Right: Description -->
				<div class="lg:col-span-3 flex items-top">
					<div class="space-y-4">
						<p
							class="text-2xl"
							style="color: var(--color-text-primary);"
						>
							{aboutContent}
						</p>
						<p
							class="font-body text-xl leading-relaxed"
							style="color: var(--color-text-primary);"
						>
							{$_("about.description_2")}
						</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Books Section -->
	<section class="books-section">
		<!-- Loading State -->
		{#if isLoading}
			<div class="book-screen-container">
				<div class="text-center">
					<div
						class="inline-flex items-center px-4 py-2 font-book-text text-sm"
						style="color: var(--color-text-secondary);"
					>
						<div
							class="animate-spin rounded-full h-4 w-4 border-b-2 mr-3"
						></div>
						Loading books...
					</div>
				</div>
			</div>
		{:else if bookProducts.length === 0}
			<div class="book-screen-container">
				<div class="text-center">
					<p
						class="font-book-text text-base"
						style="color: var(--color-text-secondary);"
					>
						{$_("home.no_products")}
					</p>
				</div>
			</div>
		{:else}
			<!-- Books List - Each book takes full screen on wide screens -->
			{#each bookProducts as product, index}
				<!-- Book Card with Alternating Layout -->
				<div class="book-screen-container animate-on-scroll book-card">
					<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-auto min-h-96">
						<div
							class="card-classic overflow-hidden h-auto flex items-top min-h-96"
							style="background: var(--color-bg-primary); border-style: hidden;"
						>
							<div
								class="md:flex {index % 2 === 1
									? 'md:flex-row-reverse'
									: ''} h-auto items-top min-h-96"
							>
								<!-- Book Cover -->
								<div
									class="md:w-1/3 flex items-top justify-start animate-on-scroll book-image"
								>
									<div class="text-center">
										<div
											class="w-auto h-auto m-6 flex items-center justify-center overflow-hidden relative"
											style="background: var(--color-bg-primary);"
										>
											{#if product.cover_image}
												<!-- Product Image -->
												<img
													src={product.cover_image}
													alt={product.title}
													class="w-full h-full object-cover transition-opacity duration-300"
													loading="lazy"
													on:error={(e) => {
														console.warn(
															"❌ Failed to load product image:",
															product.cover_image,
														);
														const target =
															e.target as HTMLImageElement;
														const fallback =
															target.nextElementSibling as HTMLElement;
														if (target && fallback) {
															target.style.display =
																"none";
															fallback.style.display =
																"block";
														}
													}}
												/>

												<!-- Fallback icon (hidden by default) -->
												<div
													class="text-center hidden"
													style="color: var(--color-text-primary);"
												>
													<div
														class="font-title text-4xl mb-2"
													>
														📖
													</div>
													<div
														class="font-title text-sm font-medium px-2"
													>
														{product.title}
													</div>
													<div
														class="font-book-text text-xs mt-1"
														style="color: var(--color-text-light);"
													>
														Image unavailable
													</div>
												</div>
											{:else}
												<!-- Default book icon when no image -->
												<div
													class="text-center"
													style="color: var(--color-text-primary);"
												>
													<div
														class="font-title text-4xl mb-2"
													>
														📖
													</div>
													<div
														class="font-title text-sm font-medium px-2"
													>
														{product.title}
													</div>
												</div>
											{/if}
										</div>
									</div>
								</div>

								<!-- Book Info -->
								<div class="md:w-2/3 mt-4 justify-between px-16 ">
									<p class="text-2xl leading-relaxed mb-2 mt-4"
										style="color: var(--color-text-primary);">{product.subtitle}</p>
									<h1 class="font-title-blue font-bold lg:text-5xl text-4xl mb-4 py-4">
										{product.title}
									</h1>
									<!-- Description -->
									<p
										class="text-2xl mb-16"
										style="color: var(--color-text-primary);"
									>
										{product.description.substring(0, 200) + (product.description.length > 200 ? '...' : '')}
									</p>

									<div
										class="flex flex-col sm:flex-row gap-4"
									>
										<button
											class="btn-primary"
											on:click={() =>
												goto(`/book/${product.id}`)}
										>
											{$_("home.preview_chapters")}
										</button>
										<button
											class="btn-secondary"
											on:click={() => addToCart(product)}
											disabled={$cartLoading}
											class:opacity-50={$cartLoading}
										>
											{#if $cartLoading}
												<div
													class="inline-flex items-center"
												>
													<div
														class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"
													></div>
													{$_("home.adding_to_cart")}
												</div>
											{:else}
												{$_("common.add_to_cart")} - {product.formatted_price ||
													formatPriceWithCurrency(
														product.price,
													)}
											{/if}
										</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			{/each}
		{/if}
	</section>

	<!-- Scroll Indicator -->
	{#if showScrollIndicator}
		<div class="scroll-indicator" on:click={scrollToNextSection} on:keydown={(e) => e.key === 'Enter' && scrollToNextSection()} role="button" tabindex="0">
			<div class="scroll-arrow">
				<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path d="M7 10L12 15L17 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
				</svg>
			</div>
		</div>
	{/if}
</div>

<style>
	/* Mobile optimizations */
	@media (max-width: 768px) {
		h1 {
			font-size: 2.5rem;
		}
	}
	/* Hero Video Section Styles */
	.hero-video-container {
		position: relative;
		width: 100%;
		height: calc(100vh - 4rem); /* Subtract navigation bar height (h-16 = 4rem) */
		max-height: calc(100vh - 4rem); /* Limit height on wide screens */
		overflow: hidden;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.video-wrapper {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 1;
	}

	#myVideo {
		width: 100%;
		height: 100%;
		object-fit: cover;
		object-position: center;
	}

	.hero-overlay {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		height: 33.33%; /* Bottom 1/3 of the video */
		z-index: 10;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(transparent, rgba(0, 0, 0, 0.4));
		padding: 2rem;
	}

	.hero-content {
		text-align: center;
		max-width: 90%; 
		width: 100%;
	}

	/* About Section Styles */
	.about-section {
		min-height: auto; /* Default for mobile/portrait */
	}

	/* Book Section Styles */
	.books-section {
		position: relative;
	}

	.book-screen-container {
		min-height: auto; /* Default for mobile/portrait */
		padding: 0 0;
	}

	/* Wide Screen Styles - Landscape orientation and wide screens */
	@media (min-width: 1024px) and (orientation: landscape) {
		.about-section {
			height: 100vh;
			display: flex;
			min-height: 450px;
			margin-bottom: 2rem;
		}

		.book-screen-container {
			height: 95vh;
			display: flex;
			padding: 0;
			flex-shrink: 1;
			min-height: 500px;
		}
	}

	/* Responsive adjustments */
	@media (max-width: 768px) {
		.hero-video-container {
			height: calc(70vh - 4rem); /* Account for navigation bar on mobile */
			max-height: calc(70vh - 4rem);
		}

		.hero-overlay {
			height: 40%; /* Slightly larger on mobile for better readability */
			padding: 1rem;
		}

		.hero-content {
			max-width: none;
		}

		/* Ensure mobile keeps scrollable layout */
		.about-section {
			min-height: auto;
			height: auto;
		}

		.book-screen-container {
			min-height: auto;
			height: auto;
			padding: 0 0;
		}
	}

	/* Portrait tablets - keep scrollable layout */
	@media (max-width: 1024px) and (orientation: portrait) {
		.about-section {
			min-height: auto;
			height: auto;
		}

		.book-screen-container {
			min-height: auto;
			height: auto;
			padding: 2rem 0;
		}
	}

	/* Scroll Indicator Styles */
	.scroll-indicator {
		position: fixed;
		bottom: 2rem;
		left: 5%;
		transform: translateX(-50%);
		z-index: 100;
		display: flex;
		flex-direction: column;
		align-items: center;
		cursor: pointer;
		transition: all 0.3s ease;
		opacity: 0.7;
		color: var(--color-text-primary);
	}

	.scroll-indicator:hover {
		opacity: 1;
		transform: translateX(-50%) translateY(-5px);
	}

	.scroll-arrow {
		width: 48px;
		height: 48px;
		border-radius: 50%;
		background: rgba(246, 242, 233, 0.8);
		backdrop-filter: blur(15px) saturate(180%);
		-webkit-backdrop-filter: blur(15px) saturate(180%);
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 0.5rem;
		box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
		border: 2px solid rgba(141, 110, 83, 0.3);
		animation: gentle-pulse 2s ease-in-out infinite;
		position: relative;
	}

	.scroll-arrow::before {
		content: '';
		position: absolute;
		top: -2px;
		left: -2px;
		right: -2px;
		bottom: -2px;
		background: linear-gradient(45deg, rgba(98, 174, 200, 0.3), rgba(141, 110, 83, 0.3));
		border-radius: 50%;
		z-index: -1;
		filter: blur(4px);
	}



	@keyframes gentle-pulse {
		0%, 100% {
			transform: translateY(0);
			box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
		}
		50% {
			transform: translateY(-3px);
			box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
		}
	}

	/* Smooth scrolling for the entire page */
	:global(html) {
		scroll-behavior: smooth;
	}

	/* Add subtle fade-in animations for sections */
	#hero {
		animation: fadeInUp 0.8s ease-out forwards;
	}

	#about {
		animation: fadeInUp 0.8s ease-out forwards;
		animation-delay: 0.3s;
	}

	.book-screen-container {
		animation: fadeInUp 0.8s ease-out forwards;
	}

	.book-screen-container:nth-of-type(1) {
		animation-delay: 0.6s;
	}

	.book-screen-container:nth-of-type(2) {
		animation-delay: 0.9s;
	}

	.book-screen-container:nth-of-type(3) {
		animation-delay: 1.2s;
	}

	.book-screen-container:nth-of-type(4) {
		animation-delay: 1.5s;
	}

	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(50px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	/* Scroll-triggered animations */
	.animate-on-scroll {
		opacity: 0;
		transform: translateY(50px);
		transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	:global(.animate-on-scroll.animate-in) {
		opacity: 1;
		transform: translateY(0);
	}

	/* Staggered animation for book cards */
	.book-card {
		transition-delay: 0.2s;
	}

	.book-card:nth-child(even) {
		transform: translateY(50px) translateX(-30px);
	}

	.book-card:nth-child(odd) {
		transform: translateY(50px) translateX(30px);
	}

	:global(.book-card.animate-in) {
		transform: translateY(0) translateX(0);
	}

	/* Parallax elements */
	.book-image {
		transition: transform 0.1s ease-out;
	}

	/* Initial state for animated elements */
	#hero,
	#about,
	.book-screen-container {
		opacity: 0;
		transform: translateY(50px);
	}

	/* Enhanced scroll effects */
	.hero-overlay {
		transition: transform 0.3s ease-out;
	}

	/* Smooth transitions for all interactive elements */
	.card-classic,
	.decorative-border,
	iframe {
		transition: transform 0.3s ease-out, box-shadow 0.3s ease-out;
	}

	.card-classic:hover {
		transform: translateY(-5px);
		box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
	}

	/* Hide scroll indicator on mobile */
	@media (max-width: 768px) {
		.scroll-indicator {
			display: none;
		}
	}

</style>
