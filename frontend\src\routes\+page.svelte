<script lang="ts">
	import { onMount } from "svelte";
	import { _ } from "$lib/i18n";
	import { getCurrentLocale, isRTL } from "$lib/i18n";
	import { goto } from "$app/navigation";
	import {
		productsStore,
		productActions,
		isLoadingProducts,
	} from "$lib/stores/products";
	import {
		formatPriceWithCurrency,
		currentCurrency,
		currentRegion,
	} from "$lib/stores/currency";
	import { cartActions, cartLoading } from "$lib/stores/cart";
	import type { Product } from "$lib/types/products";
	import { apiClient } from "$lib/api/client";

	let currentLocale = "en";
	let rtl = false;
	let videoUrl = ""; // Default video for about section
	let heroVideoUrl = ""; // Hero video URL from store settings
	let aboutContent = ""; // About content from store settings
	let addToCartSuccess = false;
	let oldVideo = false;

	$: currentLocale = getCurrentLocale();
	$: rtl = isRTL(currentLocale);
	$: allProducts = $productsStore;
	$: isLoading = $isLoadingProducts;
	$: currency = $currentCurrency;
	$: region = $currentRegion;

	// Filter products to show only "Book" type on main page
	// Admin can control what appears by setting product type to "Book" in MedusaJS admin panel
	$: bookProducts = allProducts.filter((product) => {
		const productType = product.type?.toLowerCase();
		return productType === "book";
	});

	// Reload products when region changes (for updated pricing)
	$: if (region) {
		console.log(
			"🏠 Main page: Region changed to",
			region.name,
			"Currency:",
			currency,
		);
		console.log(
			"🏠 Main page: Products will reload automatically for updated pricing",
		);
	}

	// Log currency changes
	$: if (currency) {
		console.log("💰 Main page: Currency updated to", currency);
	}

	// Add product to cart
	async function addToCart(product: Product) {
		if (!product) {
			console.warn("⚠️ No product selected to add to cart");
			return;
		}

		try {
			await cartActions.addItem(product, 1);
			console.log("✅ Added to cart:", product.title);

			// Show success feedback
			addToCartSuccess = true;
			setTimeout(() => {
				addToCartSuccess = false;
			}, 2000); // Hide success message after 2 seconds
		} catch (error) {
			console.error("❌ Failed to add to cart:", error);
			// Optional: Show error feedback to user
		}
	}

	onMount(async () => {
		// Load products from MedusaJS
		console.log("🏠 Loading products for main page...");
		await productActions.loadProducts();

		// Fetch video URLs from store settings (custom endpoint)
		try {
			console.log("Attempting to fetch store settings...");
			const response = await apiClient.getStoreSettings();

			if (response.data && response.data.settings) {
				console.log("Store settings:", response.data);
				const settings = response.data.settings;
				if (settings?.main_video_url) {
					videoUrl = settings.main_video_url;
					console.log(
						"About section video URL from settings:",
						videoUrl,
					);
				}
				if (settings?.hero_video_url) {
					heroVideoUrl = settings.hero_video_url;
					console.log("Hero video URL from settings:", heroVideoUrl);
				}
				if (settings?.about_content) {
					aboutContent = settings.about_content;
				}
			} else {
				console.log("No store settings or error:", response.error);
			}
		} catch (error) {
			console.log("Error fetching store settings:", error);
			console.log("Using default video URLs");
		}
	});
</script>

<svelte:head>
	<title>{$_("home.title")} - Hebrew Book Store</title>
	<meta name="description" content={$_("home.description")} />
</svelte:head>

<div
	class="min-h-screen"
	style="background: var(--color-bg-primary);"
	class:rtl
>
	<!-- Hero Video Section -->
	{#if heroVideoUrl}
		<section id="hero" class="hero-video-container">
			<div class="video-wrapper">
				<!-- Video -->
				<video
					autoplay
					muted
					loop
					src={heroVideoUrl}
					title="Hero Video"
					id="myVideo"
				></video>
			</div>
			<!-- Overlay positioned in bottom 1/3 -->
			<div class="hero-overlay">
				<div class="hero-content">
					<h1
						class="font-title text-2xl md:text-4xl lg:text-6xl font-bold drop-shadow-lg mb-6"
						style="color: var(--color-background);"
					>
						{$_("hero.title")}
					</h1>
					<button class="btn-hero" on:click={() => goto("/catalog")}>
						{$_("hero.cta_button")}
					</button>
				</div>
			</div>
		</section>
	{/if}

	<!-- About Us Section -->
	<section id="about" class="py-16 px-4 sm:px-6 lg:px-8">
		<div class="max-w-7xl mx-auto">
			<div class="grid grid-cols-1 lg:grid-cols-12 gap-8">
				<!-- Left: Title -->
				<div
					class="lg:col-span-3 flex flex-col items-end justify-start"
				>
					<h1
						class="font-title text-4xl md:text-5xl lg:text-6xl font-medium text-center lg:text-left"
						style="color: var(--color-text-primary);"
					>
						{$_("about.title")}
					</h1>
				</div>

				<!-- Center: Video Section -->
				<div class="lg:col-span-6">
					<div class="relative">
						<div
							class="decorative-border overflow-hidden"
							style="border-color: var(--color-border);"
						>
							<div class="aspect-w-16 aspect-h-9">
								<iframe
									src={videoUrl}
									title={$_("about.video_title")}
									frameborder="0"
									allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
									allowfullscreen
									class="w-full h-full"
									style="aspect-ratio: 16/9; min-height: 300px;"
								></iframe>
							</div>
						</div>
					</div>
				</div>

				<!-- Right: Description -->
				<div class="lg:col-span-3 flex items-top">
					<div class="space-y-4">
						<p
							class="font-body text-xl leading-relaxed"
							style="color: var(--color-text-secondary);"
						>
							{aboutContent}
						</p>
						<p
							class="font-body text-xl leading-relaxed"
							style="color: var(--color-text-secondary);"
						>
							{$_("about.description_2")}
						</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Books Section -->
	<section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
		<!-- Loading State -->
		{#if isLoading}
			<div class="text-center mb-12">
				<div
					class="inline-flex items-center px-4 py-2 font-book-text text-sm"
					style="color: var(--color-text-secondary);"
				>
					<div
						class="animate-spin rounded-full h-4 w-4 border-b-2 mr-3"
					></div>
					Loading books...
				</div>
			</div>
		{:else if bookProducts.length === 0}
			<div class="text-center mb-12">
				<p
					class="font-book-text text-base"
					style="color: var(--color-text-secondary);"
				>
					{$_("home.no_products")}
				</p>
			</div>
		{:else}
			<!-- Books List - Scrollable Layout -->
			<div class="space-y-12">
				{#each bookProducts as product, index}
					<!-- Book Card with Alternating Layout -->
					<div
						class="card-classic overflow-hidden mb-8"
						style="background: var(--color-bg-primary); border-style: hidden;"
					>
						<div
							class="md:flex {index % 2 === 1
								? 'md:flex-row-reverse'
								: ''}"
						>
							<!-- Book Cover -->
							<div
								class="md:w-1/3 p-8 flex items-center justify-center"
							>
								<div class="text-center">
									<div
										class="w-auto h-auto m-6 flex items-center justify-center overflow-hidden relative"
										style="background: var(--color-bg-primary);"
									>
										{#if product.cover_image}
											<!-- Product Image -->
											<img
												src={product.cover_image}
												alt={product.title}
												class="w-full h-full object-cover transition-opacity duration-300"
												loading="lazy"
												on:error={(e) => {
													console.warn(
														"❌ Failed to load product image:",
														product.cover_image,
													);
													const target =
														e.target as HTMLImageElement;
													const fallback =
														target.nextElementSibling as HTMLElement;
													if (target && fallback) {
														target.style.display =
															"none";
														fallback.style.display =
															"block";
													}
												}}
											/>

											<!-- Fallback icon (hidden by default) -->
											<div
												class="text-center hidden"
												style="color: var(--color-text-primary);"
											>
												<div
													class="font-title text-4xl mb-2"
												>
													📖
												</div>
												<div
													class="font-title text-sm font-medium px-2"
												>
													{product.title}
												</div>
												<div
													class="font-book-text text-xs mt-1"
													style="color: var(--color-text-light);"
												>
													Image unavailable
												</div>
											</div>
										{:else}
											<!-- Default book icon when no image -->
											<div
												class="text-center"
												style="color: var(--color-text-primary);"
											>
												<div
													class="font-title text-4xl mb-2"
												>
													📖
												</div>
												<div
													class="font-title text-sm font-medium px-2"
												>
													{product.title}
												</div>
											</div>
										{/if}
									</div>
									<p
										class="font-book-text text-base"
										style="color: var(--color-text-secondary);"
									>
										{product.short_description}
									</p>
								</div>
							</div>

							<!-- Book Info -->
							<div class="md:w-2/3 p-8 justify-between">
								<h1 class="font-title-blue text-4xl mb-2">
									{product.title}
								</h1>
								<!-- Description -->
								<p
									class="font-body text-base leading-relaxed mb-8"
									style="color: var(--color-text-secondary);"
								>
									{product.description}
								</p>

								<div
									class="flex flex-col sm:flex-row gap-4 align-bottom items-bottom justify-bottom"
								>
									<button
										class="btn-primary"
										on:click={() =>
											goto(`/book/${product.id}`)}
									>
										{$_("home.preview_chapters")}
									</button>
									<button
										class="btn-secondary"
										on:click={() => addToCart(product)}
										disabled={$cartLoading}
										class:opacity-50={$cartLoading}
									>
										{#if $cartLoading}
											<div
												class="inline-flex items-center"
											>
												<div
													class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"
												></div>
												{$_("home.adding_to_cart")}
											</div>
										{:else}
											{$_("common.add_to_cart")} - {product.formatted_price ||
												formatPriceWithCurrency(
													product.price,
												)}
										{/if}
									</button>
								</div>
							</div>
						</div>
					</div>
				{/each}
			</div>
		{/if}
	</section>
</div>

<style>
	/* Mobile optimizations */
	@media (max-width: 768px) {
		h1 {
			font-size: 2.5rem;
		}
	}
	/* Hero Video Section Styles */
	.hero-video-container {
		position: relative;
		width: 100%;
		height: 100vh;
		max-height: 80vh; /* Limit height on wide screens */
		overflow: hidden;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.video-wrapper {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 1;
	}

	#myVideo {
		width: 100%;
		height: 100%;
		object-fit: cover;
		object-position: center;
	}

	.hero-overlay {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		height: 33.33%; /* Bottom 1/3 of the video */
		z-index: 10;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(transparent, rgba(0, 0, 0, 0.4));
		padding: 2rem;
	}

	.hero-content {
		text-align: center;
		max-width: 6rem;
		width: 100%;
	}

	.btn-hero {
		background: var(--color-primary, #007bff);
		color: white;
		border: none;
		padding: 0.75rem 2rem;
		font-size: 1.1rem;
		font-weight: 600;
		border-radius: 0.5rem;
		cursor: pointer;
		transition: all 0.3s ease;
		text-transform: uppercase;
		letter-spacing: 0.5px;
		box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
	}

	.btn-hero:hover {
		background: var(--color-primary-dark, #0056b3);
		transform: translateY(-2px);
		box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
	}

	/* Responsive adjustments */
	@media (max-width: 768px) {
		.hero-video-container {
			height: 70vh;
			max-height: none;
		}

		.hero-overlay {
			height: 40%; /* Slightly larger on mobile for better readability */
			padding: 1rem;
		}

		.hero-content {
			max-width: none;
		}
	}

	@media (min-width: 1200px) {
		.hero-video-container {
			max-height: 75vh; /* Even more constrained on very wide screens */
		}
	}
</style>
