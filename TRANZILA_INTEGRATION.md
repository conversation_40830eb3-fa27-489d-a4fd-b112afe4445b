# Tranzila Payment Integration Guide

This document provides comprehensive instructions for integrating Tranzila payment processing with your MedusaJS e-commerce platform.

## Overview

The Tranzila integration provides:
- **Redirect Payment Flow**: Customers are redirected to Tranzila's secure payment page
- **iFrame Payment Flow**: Embedded payment form within your checkout page
- **Webhook Support**: Real-time payment status updates
- **Multi-language Support**: Hebrew and English interfaces
- **Israeli Market Focus**: Optimized for Israeli credit cards and banking

## Backend Configuration

### Environment Variables

Add the following environment variables to your backend `.env` file:

```bash
# Tranzila Configuration
TRANZILA_SUPPLIER=your_supplier_id
TRANZILA_USER=your_user_name
TRANZILA_PASSWORD=your_password
TRANZILA_CURRENCY=ILS
TRANZILA_LANGUAGE=he
TRANZILA_TEST_MODE=true
TRANZILA_IFRAME_MODE=false

# URLs for callbacks
FRONTEND_URL=http://localhost:5173
BACKEND_URL=http://localhost:9000
```

### Required Information from Tranzila

To complete the integration, you'll need to obtain the following from Tranzila:

#### 1. Account Credentials
- **Supplier ID** (`TRANZILA_SUPPLIER`): Your unique merchant identifier
- **User Name** (`TRANZILA_USER`): API username (if using advanced features)
- **Password** (`TRANZILA_PASSWORD`): API password (if using advanced features)

#### 2. Test Environment Access
- **Test Mode**: Set `TRANZILA_TEST_MODE=true` for development
- **Test Cards**: Tranzila provides test credit card numbers for testing

#### 3. Webhook Configuration
Configure the following URLs in your Tranzila merchant panel:
- **Success URL**: `https://yourdomain.com/api/store/tranzila/callback`
- **Error URL**: `https://yourdomain.com/api/store/tranzila/callback`
- **Notify URL**: `https://yourdomain.com/webhooks/tranzila`

## Frontend Configuration

### Environment Variables

Add the following environment variables to your frontend `.env` file:

```bash
# Tranzila Frontend Configuration
VITE_TRANZILA_IFRAME_MODE=false
```

### Payment Flow Options

#### Option 1: Redirect Flow (Recommended)
- Set `VITE_TRANZILA_IFRAME_MODE=false`
- Customers are redirected to Tranzila's secure payment page
- Better security and PCI compliance
- Simpler implementation

#### Option 2: iFrame Flow
- Set `VITE_TRANZILA_IFRAME_MODE=true`
- Payment form is embedded in your checkout page
- Better user experience (no redirect)
- Requires additional security considerations

## Testing Information

### Test Credit Cards

Tranzila provides the following test credit card numbers:

```
# Successful Transactions
Card Number: ****************
Expiry: Any future date (MM/YY)
CVV: Any 3 digits

# Failed Transactions (for testing error handling)
Card Number: ****************
Expiry: Any future date
CVV: Any 3 digits
```

### Test Response Codes

Common Tranzila response codes for testing:

- `000`: Success
- `001`: Invalid credit card number
- `002`: Invalid expiry date
- `003`: Invalid CVV
- `004`: Insufficient funds
- `005`: Card declined
- `999`: System error

## Implementation Steps

### 1. Backend Setup

The backend integration is already implemented with:
- Custom Tranzila payment provider
- API routes for payment URL generation
- Webhook handling for payment notifications
- Callback handling for redirect flow

### 2. Frontend Integration

The frontend components are ready:
- `TranzilaPayment.svelte`: Main payment component
- Success and error pages for redirect flow
- Updated payment step to include Tranzila option

### 3. Configuration

1. Add environment variables to both backend and frontend
2. Configure webhook URLs in Tranzila merchant panel
3. Test with provided test cards
4. Switch to production mode when ready

## Security Considerations

### PCI Compliance
- Redirect flow: Tranzila handles all card data (recommended)
- iFrame flow: Ensure your site has proper SSL and security measures

### Webhook Security
- Verify webhook signatures (implemented in webhook handler)
- Use HTTPS for all webhook URLs
- Validate response data before processing

### Environment Security
- Never commit credentials to version control
- Use different credentials for test and production
- Regularly rotate API credentials

## Troubleshooting

### Common Issues

1. **Payment URL Generation Fails**
   - Check `TRANZILA_SUPPLIER` is correct
   - Verify backend environment variables
   - Check network connectivity to Tranzila

2. **Webhook Not Received**
   - Verify webhook URL is accessible from internet
   - Check Tranzila merchant panel configuration
   - Review server logs for errors

3. **Payment Fails**
   - Use test cards for development
   - Check response codes in logs
   - Verify currency and amount formatting

### Debug Mode

Enable debug logging by adding to your backend:

```bash
DEBUG=tranzila:*
```

## Production Deployment

### Before Going Live

1. **Test thoroughly** with test cards and test mode
2. **Configure production URLs** in Tranzila merchant panel
3. **Set production environment variables**:
   ```bash
   TRANZILA_TEST_MODE=false
   FRONTEND_URL=https://your-production-domain.com
   BACKEND_URL=https://your-api-domain.com
   ```
4. **Verify SSL certificates** are properly configured
5. **Test webhook delivery** in production environment

### Monitoring

Monitor the following in production:
- Payment success/failure rates
- Webhook delivery status
- Response times
- Error logs

## Support

### Tranzila Support
- Technical Support: Contact Tranzila technical team
- Documentation: Refer to Tranzila API documentation
- Merchant Panel: Access your Tranzila merchant account

### Integration Support
- Check server logs for detailed error messages
- Review webhook payloads for debugging
- Test with different browsers and devices

## API Reference

### Backend Endpoints

- `POST /store/tranzila/payment-url`: Generate payment URL
- `POST /webhooks/tranzila`: Handle payment notifications
- `GET/POST /api/store/tranzila/callback`: Handle redirect callbacks

### Frontend Components

- `TranzilaPayment.svelte`: Main payment component
- `/checkout/tranzila/success`: Success page
- `/checkout/tranzila/error`: Error page

## Next Steps

1. Obtain Tranzila merchant account and credentials
2. Configure environment variables
3. Test with provided test cards
4. Configure webhook URLs in Tranzila panel
5. Deploy to production with proper SSL
6. Monitor payment processing and success rates
