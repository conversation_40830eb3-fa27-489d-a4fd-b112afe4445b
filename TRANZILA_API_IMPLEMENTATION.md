# Tranzila API Implementation - Complete Server Flow

This document outlines the complete implementation of Tranzila's recommended server flow for secure payment processing.

## ✅ Implementation Status

### A. Create Transaction (Server → Tranzila) ✅
**Implemented in**: `backend/src/modules/payment-tranzila/service.ts`

```typescript
async createTransaction(request: TranzilaTransactionRequest): Promise<TranzilaTransactionResponse>
```

- ✅ Calls `https://api.tranzila.com/v1/transactions`
- ✅ Includes amount, currency, order ID, return/callback URLs
- ✅ Handles 3DS options
- ✅ Returns iframe/redirect URLs for payment form

### B. Anti-fraud "Handshake" ✅
**Implemented in**: `backend/src/modules/payment-tranzila/service.ts`

```typescript
async createHandshake(request: TranzilaHandshakeRequest): Promise<TranzilaHandshakeResponse>
```

- ✅ Calls `https://api.tranzila.com/v1/handshake/create`
- ✅ Creates unique handshake ID for amount + order
- ✅ Prevents transaction tampering
- ✅ Used before showing payment form

### C. 3-D Secure Handling ✅
**Implemented in**: Payment flow automatically handles 3DS

- ✅ Tranzila manages 3DS challenges in iframe/redirect
- ✅ Completion calls our callback URL with results
- ✅ Payment provider verifies and marks as authorized
- ✅ Integrated with Medusa payment status

### D. Webhooks / Callbacks ✅
**Implemented in**: `backend/src/api/webhooks/tranzila/route.ts`

- ✅ Endpoint: `/webhooks/tranzila`
- ✅ Handles success/failure notifications
- ✅ Treats callback as source-of-truth
- ✅ Reconciles order status in Medusa
- ✅ Supports both POST and GET requests

### E. Refunds / Voids 🔄
**Status**: Framework ready, API integration needed

- 🔄 `refundPayment` method exists in service
- 🔄 Needs Tranzila refund API integration
- 🔄 Portal refunds work independently

## 🏗️ Architecture Overview

### Payment Flow Sequence

1. **Frontend** → **Backend**: Request payment URL
2. **Backend** → **Tranzila**: Create handshake
3. **Tranzila** → **Backend**: Return handshake ID
4. **Backend** → **Tranzila**: Create transaction with handshake
5. **Tranzila** → **Backend**: Return iframe/redirect URL
6. **Backend** → **Frontend**: Return payment URL
7. **Frontend**: Load Tranzila payment form
8. **Customer**: Enter payment details
9. **Tranzila**: Process payment + 3DS if needed
10. **Tranzila** → **Backend**: Send webhook notification
11. **Backend**: Update payment status in Medusa
12. **Frontend**: Complete order

### Key Components

#### 1. Payment Provider Service
```typescript
// backend/src/modules/payment-tranzila/service.ts
class TranzilaPaymentProviderService implements IPaymentProvider {
  async createHandshake(request: TranzilaHandshakeRequest)
  async createTransaction(request: TranzilaTransactionRequest)
  async initiatePayment(context: any)
  async authorizePayment(data: any)
  async capturePayment(paymentSessionData: any)
  async refundPayment(paymentSessionData: any, refundAmount: number)
}
```

#### 2. API Endpoints
```typescript
// Payment URL generation
POST /store/tranzila/payment-url

// Webhook handler
POST /webhooks/tranzila
GET /webhooks/tranzila
```

#### 3. Frontend Integration
```typescript
// Iframe message handling
window.addEventListener('message', handleTranzilaResponse)

// Payment completion
async function handlePaymentComplete(result)
```

## 🔧 Configuration

### Environment Variables
```bash
# Required
TRANZILA_TERMINAL_NAME=vadimg
PAYMENT_PROVIDER=tranzila

# Optional
TRANZILA_API_KEY=your_api_key
TRANZILA_CURRENCY=ILS
TRANZILA_LANGUAGE=en
TRANZILA_TEST_MODE=true
TRANZILA_IFRAME_MODE=true

# URLs
MEDUSA_BACKEND_URL=http://localhost:9000
FRONTEND_URL=http://localhost:5173
```

### Medusa Configuration
```typescript
// backend/medusa-config.ts
{
  resolve: "@medusajs/medusa/payment",
  options: {
    providers: [
      {
        resolve: "./src/modules/payment-tranzila",
        id: "tranzila",
        options: {
          terminal_name: process.env.TRANZILA_TERMINAL_NAME,
          api_key: process.env.TRANZILA_API_KEY,
          currency: process.env.TRANZILA_CURRENCY || "ILS",
          language: process.env.TRANZILA_LANGUAGE || "en",
          test_mode: process.env.TRANZILA_TEST_MODE === "true",
          iframe_mode: process.env.TRANZILA_IFRAME_MODE === "true",
        },
      }
    ]
  }
}
```

## 🔒 Security Features

### 1. Handshake Anti-fraud
- Unique handshake ID prevents transaction tampering
- Amount and order validation before payment
- Recommended by Tranzila for security

### 2. Origin Validation
- Frontend validates iframe messages from Tranzila domains only
- Webhook endpoint validates request signatures

### 3. PCI Compliance
- Card data never touches your servers
- Tranzila handles all sensitive information
- Iframe isolation for payment form

## 🧪 Testing

### Test Cards (Tranzila Sandbox)
```
Success: ****************
Failure: ****************
Expiry: Any future date
CVV: Any 3 digits
```

### Test Flow
1. Add items to cart
2. Proceed to checkout
3. Select Tranzila payment
4. See embedded payment form
5. Use test card for success/failure
6. Verify webhook processing
7. Check order completion

## 🚀 Production Deployment

### Required Changes
```bash
TRANZILA_TEST_MODE=false
MEDUSA_BACKEND_URL=https://your-api-domain.com
FRONTEND_URL=https://your-frontend-domain.com
```

### Tranzila Panel Configuration
- Success URL: `https://your-frontend-domain.com/checkout/tranzila/success`
- Error URL: `https://your-frontend-domain.com/checkout/tranzila/error`
- Webhook URL: `https://your-api-domain.com/webhooks/tranzila`

## 📋 Next Steps

1. **Complete Refund API**: Integrate Tranzila refund endpoints
2. **Error Handling**: Add comprehensive error recovery
3. **Monitoring**: Add payment analytics and logging
4. **Testing**: Comprehensive integration tests
5. **Documentation**: API documentation for frontend team

This implementation follows Tranzila's recommended server flow with proper security, anti-fraud protection, and webhook handling.
