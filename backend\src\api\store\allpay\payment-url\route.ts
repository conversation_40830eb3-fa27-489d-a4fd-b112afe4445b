import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { MedusaError } from "@medusajs/framework/utils"

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const body = req.body as { 
      payment_collection_id?: string
      cart_id?: string
      amount?: number
      currency?: string
      client_name?: string
      client_email?: string
      client_phone?: string
    }
    
    const { 
      payment_collection_id, 
      cart_id, 
      amount, 
      currency,
      client_name,
      client_email,
      client_phone
    } = body

    if (!payment_collection_id && !cart_id) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "payment_collection_id or cart_id is required"
      )
    }

    console.log('🔄 Generating AllPay payment URL for:', { 
      payment_collection_id, 
      cart_id, 
      amount, 
      currency,
      client_name,
      client_email
    })

    // Generate a unique transaction ID
    const transactionId = `${payment_collection_id || cart_id}_${Date.now()}`

    // Calculate amount in currency units (AllPay expects currency units, not cents)
    const paymentAmount = amount ? Number((amount / 100).toFixed(2)) : 100.00

    // Create payment data for AllPay
    const paymentData = {
      login: process.env.ALLPAY_LOGIN || "your_allpay_login",
      order_id: transactionId,
      items: [
        {
          name: "Order Payment",
          qty: 1,
          price: paymentAmount,
          vat: 1, // 18% VAT included
        }
      ],
      currency: currency?.toUpperCase() || process.env.ALLPAY_CURRENCY || "ILS",
      lang: process.env.ALLPAY_LANGUAGE || "AUTO",
      notifications_url: `${process.env.MEDUSA_BACKEND_URL || "http://localhost:9000"}/webhooks/allpay`,
      success_url: `${process.env.FRONTEND_URL || "http://localhost:5173"}/checkout/allpay/success`,
      backlink_url: `${process.env.FRONTEND_URL || "http://localhost:5173"}/checkout`,
      client_name: client_name || "",
      client_email: client_email || "",
      client_phone: client_phone || "",
      expire: Math.floor(Date.now() / 1000) + 3600, // 1 hour expiration
    }

    // Generate signature
    const signature = generateAllPaySignature(paymentData, process.env.ALLPAY_API_KEY || "your_api_key")
    
    const requestData = {
      ...paymentData,
      sign: signature
    }

    console.log('🔄 Making AllPay API request with data:', {
      order_id: requestData.order_id,
      amount: paymentAmount,
      currency: requestData.currency
    })

    // Make request to AllPay API
    const response = await fetch('https://allpay.to/app/?show=getpayment&mode=api8', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    })

    if (!response.ok) {
      throw new Error(`AllPay API error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()

    if (!data.payment_url) {
      throw new Error(`AllPay API error: ${data.message || 'No payment URL returned'}`)
    }

    console.log('✅ AllPay payment URL generated successfully')

    res.json({
      payment_url: data.payment_url,
      order_id: transactionId,
      amount: paymentAmount,
      currency: requestData.currency,
      expires_at: new Date(Date.now() + 3600 * 1000).toISOString(),
    })

  } catch (error) {
    console.error('❌ AllPay payment URL generation failed:', error)
    
    res.status(500).json({
      error: "Failed to generate AllPay payment URL",
      message: error.message,
    })
  }
}

/**
 * Generate SHA256 signature for AllPay API requests
 */
function generateAllPaySignature(params: Record<string, any>, apiKey: string): string {
  const crypto = require('crypto')
  
  // Remove sign parameter if it exists
  const { sign, ...paramsWithoutSign } = params

  // Sort keys alphabetically
  const sortedKeys = Object.keys(paramsWithoutSign).sort()
  const chunks: string[] = []

  sortedKeys.forEach((key) => {
    const value = paramsWithoutSign[key]

    if (Array.isArray(value)) {
      value.forEach((item) => {
        if (typeof item === 'object' && item !== null) {
          const sortedItemKeys = Object.keys(item).sort()
          sortedItemKeys.forEach((itemKey) => {
            const itemValue = item[itemKey]
            if (itemValue !== null && itemValue !== undefined && String(itemValue).trim() !== '') {
              chunks.push(String(itemValue).trim())
            }
          })
        }
      })
    } else {
      if (value !== null && value !== undefined && String(value).trim() !== '') {
        chunks.push(String(value).trim())
      }
    }
  })

  // Create signature string
  const signatureString = chunks.join(':') + ':' + apiKey
  
  // Generate SHA256 hash
  return crypto.createHash('sha256').update(signatureString).digest('hex')
}
