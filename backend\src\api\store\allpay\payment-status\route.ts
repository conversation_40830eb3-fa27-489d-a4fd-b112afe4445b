import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { MedusaError } from "@medusajs/framework/utils"

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const body = req.body as { 
      order_id: string
    }
    
    const { order_id } = body

    if (!order_id) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "order_id is required"
      )
    }

    console.log('🔄 Checking AllPay payment status for order:', order_id)

    // Prepare status check request
    const statusRequest = {
      login: process.env.ALLPAY_LOGIN || "your_allpay_login",
      order_id: order_id,
    }

    // Generate signature
    const signature = generateAllPaySignature(statusRequest, process.env.ALLPAY_API_KEY || "your_api_key")
    
    const requestData = {
      ...statusRequest,
      sign: signature
    }

    console.log('🔄 Making AllPay status API request for order:', order_id)

    // Make request to AllPay status API
    const response = await fetch('https://allpay.to/app/?show=paymentstatus&mode=api8', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
    })

    if (!response.ok) {
      throw new Error(`AllPay status API error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()

    console.log('✅ AllPay payment status retrieved:', {
      order_id: data.order_id,
      status: data.status,
      amount: data.amount,
      currency: data.currency
    })

    // Map AllPay status to readable format
    let statusText = 'unknown'
    switch (data.status) {
      case 0:
        statusText = 'unpaid'
        break
      case 1:
        statusText = 'paid'
        break
      case 3:
        statusText = 'refunded'
        break
      case 4:
        statusText = 'partially_refunded'
        break
    }

    res.json({
      order_id: data.order_id,
      status: data.status,
      status_text: statusText,
      amount: data.amount,
      currency: data.currency,
      card_mask: data.card_mask,
      card_brand: data.card_brand,
      foreign_card: data.foreign_card,
      receipt: data.receipt,
      checked_at: new Date().toISOString(),
    })

  } catch (error) {
    console.error('❌ AllPay payment status check failed:', error)
    
    res.status(500).json({
      error: "Failed to check AllPay payment status",
      message: error.message,
    })
  }
}

/**
 * Generate SHA256 signature for AllPay API requests
 */
function generateAllPaySignature(params: Record<string, any>, apiKey: string): string {
  const crypto = require('crypto')
  
  // Remove sign parameter if it exists
  const { sign, ...paramsWithoutSign } = params

  // Sort keys alphabetically
  const sortedKeys = Object.keys(paramsWithoutSign).sort()
  const chunks: string[] = []

  sortedKeys.forEach((key) => {
    const value = paramsWithoutSign[key]
    if (value !== null && value !== undefined && String(value).trim() !== '') {
      chunks.push(String(value).trim())
    }
  })

  // Create signature string
  const signatureString = chunks.join(':') + ':' + apiKey
  
  // Generate SHA256 hash
  return crypto.createHash('sha256').update(signatureString).digest('hex')
}
