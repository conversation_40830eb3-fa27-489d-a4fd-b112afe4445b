import {
  IPaymentProvider,
  PaymentSessionStatus,
} from "@medusajs/framework/types"
import { MedusaError } from "@medusajs/framework/utils"
import crypto from "crypto"

export interface TranzilaOptions {
  terminal_name: string
  api_key?: string
  currency?: string
  language?: string
  test_mode?: boolean
  iframe_mode?: boolean
}

// Tranzila API interfaces
export interface TranzilaHandshakeRequest {
  terminal_name: string
  amount: string
  currency_code: string
  order_id: string
}

export interface TranzilaHandshakeResponse {
  handshake_id: string
  status: string
  message?: string
}

export interface TranzilaTransactionRequest {
  terminal_name: string
  handshake_id?: string
  amount: string
  currency_code: string
  order_id: string
  return_url: string
  callback_url: string
  language?: string
  cred_type?: string
  tranmode?: string
}

export interface TranzilaTransactionResponse {
  transaction_id: string
  iframe_url?: string
  redirect_url?: string
  status: string
  message?: string
}

export interface TranzilaPaymentData {
  terminal_name: string
  api_key?: string
  amount: string
  currency_code: string
  cred_type: string
  tranmode: string
  myid: string
  npay?: string
  fpay?: string
  spay?: string
  contact?: string
  email?: string
  phone?: string
  address?: string
  city?: string
  zip?: string
  company?: string
  remarks?: string
  lang?: string
  response?: string
  iframe?: string
  success_url?: string
  error_url?: string
  notify_url?: string
}

class TranzilaPaymentProviderService implements IPaymentProvider {
  static identifier = "tranzila"

  protected options_: TranzilaOptions
  private readonly baseUrl = "https://secure5.tranzila.com"
  private readonly testUrl = "https://secure5.tranzila.com"  // Same URL for test mode
  private readonly apiUrl = "https://api.tranzila.com/v1"

  constructor(container: any, options: TranzilaOptions) {
    this.options_ = {
      currency: "ILS",
      language: "he",
      test_mode: false,
      iframe_mode: false,
      ...options,
    }

    if (!this.options_.terminal_name) {
      throw new MedusaError(
        MedusaError.Types.INVALID_ARGUMENT,
        "Tranzila terminal_name is required"
      )
    }
  }

  // A. Create Handshake (Anti-fraud protection)
  async createHandshake(request: TranzilaHandshakeRequest): Promise<TranzilaHandshakeResponse> {
    try {
      console.log('🤝 Creating Tranzila handshake:', request)

      const response = await fetch(`${this.apiUrl}/handshake/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        throw new Error(`Handshake failed: ${response.status} ${response.statusText}`)
      }

      const result = await response.json() as TranzilaHandshakeResponse
      console.log('✅ Handshake created:', result)
      return result
    } catch (error) {
      console.error('❌ Handshake creation failed:', error)
      throw new MedusaError(
        MedusaError.Types.PAYMENT_AUTHORIZATION_ERROR,
        `Failed to create Tranzila handshake: ${error.message}`
      )
    }
  }

  // B. Create Transaction
  async createTransaction(request: TranzilaTransactionRequest): Promise<TranzilaTransactionResponse> {
    try {
      console.log('💳 Creating Tranzila transaction:', request)

      const response = await fetch(`${this.apiUrl}/transactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        throw new Error(`Transaction creation failed: ${response.status} ${response.statusText}`)
      }

      const result = await response.json() as TranzilaTransactionResponse
      console.log('✅ Transaction created:', result)
      return result
    } catch (error) {
      console.error('❌ Transaction creation failed:', error)
      throw new MedusaError(
        MedusaError.Types.PAYMENT_AUTHORIZATION_ERROR,
        `Failed to create Tranzila transaction: ${error.message}`
      )
    }
  }

  getIdentifier(): string {
    return "tranzila"
  }

  async getWebhookActionAndData(data: any): Promise<any> {
    return {
      action: "payment_update",
      data: data
    }
  }

  async getPaymentStatus(data: any): Promise<any> {
    const status = data.status as string

    switch (status) {
      case "pending":
        return { status: "pending" }
      case "authorized":
        return { status: "authorized" }
      case "captured":
        return { status: "captured" }
      case "canceled":
        return { status: "canceled" }
      case "error":
        return { status: "error" }
      default:
        return { status: "pending" }
    }
  }

  async initiatePayment(
    context: any
  ): Promise<any> {
    console.log('🔄 Initiating Tranzila payment:', context)
    try {
      const { amount, currency_code, context: paymentContext, resource_id } = context

      console.log('🔄 Initiating Tranzila payment with direct integration:', { amount, currency_code, resource_id })

      // Use direct integration (no API calls needed)
      const orderId = resource_id || `order_${Date.now()}`
      const transactionId = `${orderId}_${Date.now()}`

      // Prepare payment data for direct integration
      const paymentData = {
        terminal_name: this.options_.terminal_name,
        sum: (amount ).toFixed(2), // Tranzila uses 'sum' parameter
        currency: currency_code?.toUpperCase() || this.options_.currency || "ILS", // Tranzila uses 'currency' parameter
        myid: transactionId,
        cred_type: "1", // Credit card
        tranmode: "A", // Authorization
        response: "1", // Return response data
        // Enable iframe callbacks
        iframe_callback: this.options_.iframe_mode ? "1" : "0",
        return_url: `${process.env.FRONTEND_URL}/checkout/tranzila/success`,
      }

      // Generate the payment URL based on iframe mode
      let paymentUrl: string

      if (this.options_.iframe_mode) {
        // Generate iframe URL
        const params = new URLSearchParams()
        Object.entries(paymentData).forEach(([key, value]) => {
          if (value !== undefined && value !== null && key !== 'terminal_name') {
            params.append(key, value.toString())
          }
        })
        paymentUrl = `https://direct.tranzila.com/${this.options_.terminal_name}/iframenew.php?${params.toString()}`
      } else {
        // Generate redirect URL
        const params = new URLSearchParams()
        Object.entries(paymentData).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            params.append(key, value.toString())
          }
        })
        paymentUrl = `https://secure5.tranzila.com/cgi-bin/tranzila71u.cgi?${params.toString()}`
      }

      console.log('✅ Tranzila payment URL generated:', paymentUrl)

      return {
        data: {
          id: orderId,
          transaction_id: transactionId,
          payment_url: paymentUrl,
          iframe_url: this.options_.iframe_mode ? paymentUrl : undefined,
          redirect_url: !this.options_.iframe_mode ? paymentUrl : undefined,
          payment_data: paymentData,
          status: "pending",
          amount: amount,
          currency: currency_code,
          created_at: new Date().toISOString(),
        },
      }
    } catch (error) {
      console.error('❌ Error initiating Tranzila payment:', error)
      return this.buildError("Failed to initiate Tranzila payment", error)
    }
  }

  async authorizePayment(data: any): Promise<any> {
    try {
      // For Tranzila, authorization happens on their side
      // We just need to verify the payment was successful
      const { payment_data } = data as any

      return {
        session_data: {
          ...data,
          status: "authorized",
          authorized_at: new Date().toISOString(),
        },
      }
    } catch (error) {
      return this.buildError("Failed to authorize Tranzila payment", error)
    }
  }

  async capturePayment(
    paymentSessionData: Record<string, unknown>
  ): Promise<any> {
    try {
      // For immediate capture, we would use tranmode "AK" in initiatePayment
      // For later capture, we would need to make a separate API call to Tranzila
      
      return {
        session_data: {
          ...paymentSessionData,
          status: "captured",
          captured_at: new Date().toISOString(),
        },
      }
    } catch (error) {
      return this.buildError("Failed to capture Tranzila payment", error)
    }
  }

  async refundPayment(data: any): Promise<any> {
    try {
      // Tranzila refunds would require a separate API call
      // This is a placeholder implementation
      const refundAmount = data.amount || 0

      return {
        session_data: {
          ...data,
          status: "refunded",
          refunded_amount: refundAmount,
          refunded_at: new Date().toISOString(),
        },
      }
    } catch (error) {
      return this.buildError("Failed to refund Tranzila payment", error)
    }
  }

  async cancelPayment(
    paymentSessionData: Record<string, unknown>
  ): Promise<any> {
    try {
      return {
        session_data: {
          ...paymentSessionData,
          status: "canceled",
          canceled_at: new Date().toISOString(),
        },
      }
    } catch (error) {
      return this.buildError("Failed to cancel Tranzila payment", error)
    }
  }

  async deletePayment(
    paymentSessionData: Record<string, unknown>
  ): Promise<any> {
    try {
      // Clean up any resources if needed
      return
    } catch (error) {
      return this.buildError("Failed to delete Tranzila payment", error)
    }
  }

  async retrievePayment(
    paymentSessionData: Record<string, unknown>
  ): Promise<any> {
    try {
      return {
        session_data: paymentSessionData,
      }
    } catch (error) {
      return this.buildError("Failed to retrieve Tranzila payment", error)
    }
  }

  async updatePayment(
    context: any
  ): Promise<any> {
    try {
      const { amount, currency_code, data } = context
      
      return {
        session_data: {
          ...data,
          amount: amount,
          currency: currency_code,
          updated_at: new Date().toISOString(),
        },
      }
    } catch (error) {
      return this.buildError("Failed to update Tranzila payment", error)
    }
  }

  // Helper method to generate Tranzila payment URL
  generatePaymentUrl(paymentData: TranzilaPaymentData): string {
    const baseUrl = this.options_.test_mode ? this.testUrl : this.baseUrl
    const params = new URLSearchParams()
    
    Object.entries(paymentData).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString())
      }
    })
    
    return `${baseUrl}/cgi-bin/tranzila71u.cgi?${params.toString()}`
  }

  // Helper method to verify Tranzila response
  verifyResponse(responseData: Record<string, string>): boolean {
    // Implement response verification logic based on Tranzila documentation
    // This typically involves checking response codes and signatures
    return responseData.Response === "000" // Success response code
  }

  private buildError(message: string, error: any): any {
    return {
      error: message,
      code: "tranzila_error",
      detail: error?.message || error,
    }
  }
}

export default TranzilaPaymentProviderService
