<script lang="ts">
	import { page } from "$app/stores";
	import { _ } from "$lib/i18n";
	import { getCurrentLocale, isRTL, setLocale } from "$lib/i18n";
	import { goto } from "$app/navigation";
	import {
		isAuthenticated,
		currentCustomer,
		authStore,
	} from "$lib/stores/auth";
	import CartIcon from "./CartIcon.svelte";
	import Cart from "./Cart.svelte";
	import RegionSelector from "./RegionSelector.svelte";
	import AuthModal from "./AuthModal.svelte";

	let currentLocale = "en";
	let rtl = false;
	let mobileMenuOpen = false;
	let cartOpen = false;
	let authModalOpen = false;
	let authMode: "login" | "register" = "login";

	$: currentLocale = getCurrentLocale();
	$: rtl = isRTL(currentLocale);
	$: currentPath = $page.url.pathname;

	function handleLanguageChange(newLocale: string) {
		setLocale(newLocale);
	}

	function toggleMobileMenu() {
		mobileMenuOpen = !mobileMenuOpen;
	}

	function closeMobileMenu() {
		mobileMenuOpen = false;
	}

	function openCart() {
		cartOpen = true;
	}

	function closeCart() {
		cartOpen = false;
	}

	function openAuthModal(mode: "login" | "register") {
		authMode = mode;
		authModalOpen = true;
	}

	function closeAuthModal() {
		authModalOpen = false;
	}

	function handleAuthSuccess() {
		authModalOpen = false;
		// Could show a success message here
	}

	async function handleLogout() {
		await authStore.logout();
		// Could show a logout message here
	}

	function isActive(path: string): boolean {
		return (
			currentPath === path ||
			(path !== "/" && currentPath.startsWith(path))
		);
	}

	function handleNavClick(path: string, event: Event) {
		if (path === "/#about") {
			event.preventDefault();
			// If we're not on the home page, navigate there first
			if (currentPath !== "/") {
				goto("/").then(() => {
					setTimeout(() => scrollToAbout(), 100);
				});
			} else {
				scrollToAbout();
			}
		}
		// Always close mobile menu on any navigation click
		closeMobileMenu();
	}

	function scrollToAbout() {
		const aboutSection = document.getElementById("about");
		if (aboutSection) {
			aboutSection.scrollIntoView({ behavior: "smooth" });
		}
	}

	const navItems = [
		{ path: "/", label: "navigation.home" },
		{ path: "/catalog", label: "navigation.catalog" },
		{ path: "/#about", label: "navigation.about" },
		{ path: "/contact", label: "navigation.contact" },
	];
</script>

<nav
	class="shadow-lg sticky top-0 z-50"
	style="background-color: var(--color-navigation);"
	class:rtl
>
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-18">
		<div class="flex items-center justify-between h-16">
			<!-- Left: Burger Menu -->
			<div class="flex items-center h-10 w-auto">
				<button
					on:click={toggleMobileMenu}
					class="p-1 rounded-md transition-colors hover:bg-black hover:bg-opacity-10 h-18 w-18"
					aria-label="Open menu"
				>
					<svg
						class="w-18 h-8"
						style="color: var(--color-text-light);"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 20"
					>
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="3"
							d="M4 6h16M4 12h16M4 18h16"
						/>
					</svg>
				</button>
			</div>

			<!-- Center: Logo -->
			<div
				class="flex items-center border-4 rounded-full p-0 lg:ml-28 md:ml-28"
				style="border-color: var(--color-text-light);"
			>
				<button
					on:click={() => goto("/")}
					class="flex items-center transition-opacity hover:opacity-80"
					aria-label="Go to homepage"
				>
					<img
						src="/images/logo-dark.PNG"
						alt="Hebrew Book Store Logo"
						class="h-auto w-24 p-1 ml-2 mr-2"
					/>
				</button>
			</div>

			<!-- Right: Cart, Auth, Language Switcher -->
			<div
				class="flex items-center space-x-4"
				class:space-x-reverse={rtl}
			>
				<!-- Cart Icon -->
				<CartIcon on:open={openCart} />

				<!-- Authentication -->
				{#if $isAuthenticated}
					<!-- User Account Button -->
					<button
						on:click={() => goto("/account")}
						class="p-2 rounded-md transition-colors hover:bg-black hover:bg-opacity-10"
						title={$_("account.my_account")}
						aria-label={$_("account.my_account")}
					>
						<div class="flex items-center">
							<svg
								class="w-8 h-8"
								style="color: var(--color-text-light);"
								fill="none"
								stroke="currentColor"
								viewBox="0 0 20 20"
							>
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
								/>
							</svg>
							<p
								style="color: var(--color-text-light)"
								class="hidden md:block font-medium ml-2"
							>
								{$_("account.my_account")}
							</p>
						</div>
					</button>
				{:else}
					<!-- Login/Register Button -->

					<button
						on:click={() => openAuthModal("login")}
						class="p-2 rounded-md transition-colors hover:bg-black hover:bg-opacity-10"
						aria-label="Sign in to account"
					>
						<div class="flex items-center">
							<img
								src="/images/account light.png"
								alt="Account"
								class="w-8 h-8"
							/>
							<p
								style="color: var(--color-text-light)"
								class="hidden md:block font-medium ml-2"
							>
								{$_("auth.sign_in")}
							</p>
						</div>
					</button>
				{/if}

				<!-- Language Switcher -->
				<div class="flex items-center font-medium ml-2">
					<select
						class="bg-transparent text-sm font-body border-0 focus:outline-none focus:ring-0 cursor-pointer font-medium ml-2"
						style="color: var(--color-text-light);"
						value={currentLocale}
						on:change={(e) =>
							handleLanguageChange(
								(e.target as HTMLSelectElement).value,
							)}
					>
						<option
							value="en"
							style="color: var(--color-text-primary); background: var(--color-bg-primary);"
							class="font-medium ml-2">EN</option
						>
						<option
							value="he"
							style="color: var(--color-text-primary); background: var(--color-bg-primary);"
							class="font-medium ml-2">עב</option
						>
						<option
							value="ru"
							style="color: var(--color-text-primary); background: var(--color-bg-primary);"
							class="font-medium ml-2">РУ</option
						>
					</select>
				</div>
			</div>
		</div>

		<!-- Navigation Menu (Mobile and Desktop) -->
		{#if mobileMenuOpen}
			<div class="absolute left-0 top-full z-50 w-64 md:w-80">
				<div
					class="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t shadow-lg"
					style="background-color: var(--color-navigation); border-color: rgba(246, 242, 233, 0.2);"
				>
					{#each navItems as item}
						<a
							href={item.path}
							class="block px-3 py-2 rounded-md text-base font-body font-medium transition-colors duration-200"
							class:accent-text={isActive(item.path)}
							style={isActive(item.path)
								? "background-color: rgba(98, 174, 200, 0.1);"
								: ""}
							class:hover:accent-text={!isActive(item.path)}
							style:color={!isActive(item.path)
								? "var(--color-text-light)"
								: ""}
							class:text-right={rtl}
							on:click={(e) => handleNavClick(item.path, e)}
						>
							{$_(item.label)}
						</a>
					{/each}

					<!-- Mobile Authentication -->
					<div
						class="px-3 py-2 border-t"
						style="border-color: rgba(246, 242, 233, 0.2);"
					>
						{#if $isAuthenticated}
							<div class="space-y-2">
								<a
									href="/account"
									class="block px-3 py-2 rounded-md text-base font-body font-medium transition-colors"
									style="color: var(--color-text-light); hover:background-color: rgba(98, 174, 200, 0.1);"
									class:text-right={rtl}
									on:click={closeMobileMenu}
								>
									<div class="flex items-center">
										<img
											src="/images/account light.png"
											alt="Account"
											class="w-8 h-8"
										/>
										<p
											style="color: var(--color-text-light)"
											class="hidden md:block font-medium ml-2"
										>
											{$currentCustomer?.first_name ||
												$_("account.account")}
										</p>
									</div>
								</a>
								<button
									on:click={() => {
										handleLogout();
										closeMobileMenu();
									}}
									class="block w-full text-left px-3 py-2 rounded-md text-base font-body font-medium transition-colors"
									style="color: var(--color-text-light); hover:background-color: rgba(220, 38, 38, 0.1);"
									class:text-right={rtl}
								>
									{$_("auth.sign_out")}
								</button>
							</div>
						{:else}
							<div class="space-y-2">
								<button
									on:click={() => {
										openAuthModal("login");
										closeMobileMenu();
									}}
									class="block w-full text-left px-3 py-2 rounded-md text-base font-body font-medium transition-colors"
									style="color: var(--color-text-light); hover:background-color: rgba(98, 174, 200, 0.1);"
									class:text-right={rtl}
								>
									{$_("auth.sign_in")}
								</button>
								<button
									on:click={() => {
										openAuthModal("register");
										closeMobileMenu();
									}}
									class="block w-full text-left px-3 py-2 rounded-md text-base font-body font-medium transition-colors accent-text"
									style="background-color: rgba(98, 174, 200, 0.1); hover:background-color: rgba(98, 174, 200, 0.2);"
									class:text-right={rtl}
								>
									{$_("auth.create_account")}
								</button>
							</div>
						{/if}
					</div>

					<!-- Mobile Region Selector -->
					<div
						class="px-3 py-2 border-t"
						style="border-color: rgba(246, 242, 233, 0.2);"
					>
						<RegionSelector />
					</div>
				</div>
			</div>
		{/if}
	</div>
</nav>

<!-- Cart Component -->
<Cart bind:isOpen={cartOpen} on:close={closeCart} />

<!-- Authentication Modal -->
<AuthModal
	bind:isOpen={authModalOpen}
	bind:mode={authMode}
	on:close={closeAuthModal}
	on:success={handleAuthSuccess}
/>

<style>
	.rtl {
		direction: rtl;
	}
</style>
