# AllPay Payment Provider Integration

This document outlines the complete implementation of AllPay payment provider integration for the MedusaJS-based book store application.

## Overview

AllPay is an Israeli payment gateway that supports:
- Israeli and international credit cards
- Multiple currencies (ILS, USD, EUR)
- Iframe and redirect payment flows
- Webhooks for payment notifications
- Refunds and partial refunds
- Test mode for development

## ✅ Implementation Status

### Backend Implementation ✅

#### 1. Payment Provider Service
**File**: `backend/src/modules/payment-allpay/service.ts`
- ✅ Implements `IPaymentProvider` interface
- ✅ SHA256 signature generation
- ✅ Payment initiation, authorization, capture
- ✅ Refund and cancellation support
- ✅ Payment status verification
- ✅ Webhook signature verification

#### 2. API Routes
**Files**: 
- `backend/src/api/store/allpay/payment-url/route.ts` - Payment URL generation
- `backend/src/api/webhooks/allpay/route.ts` - Webhook handling
- `backend/src/api/store/allpay/payment-status/route.ts` - Status verification

#### 3. Configuration
**File**: `backend/medusa-config.ts`
- ✅ AllPay provider configuration added
- ✅ Environment variable integration

#### 4. Utilities
**File**: `backend/src/utils/allpay.ts`
- ✅ Signature generation utilities
- ✅ API client functions
- ✅ Test card data
- ✅ Helper functions

### Frontend Implementation ✅

#### 1. Payment Component
**File**: `frontend/src/lib/components/checkout/AllPayPayment.svelte`
- ✅ Iframe integration support
- ✅ Redirect payment flow
- ✅ Payment status verification
- ✅ Error handling and loading states
- ✅ Responsive design

#### 2. Payment Step Integration
**File**: `frontend/src/lib/components/checkout/PaymentStep.svelte`
- ✅ AllPay option added to payment providers
- ✅ Component integration

#### 3. API Client
**File**: `frontend/src/lib/api/client.ts`
- ✅ AllPay-specific API methods
- ✅ Payment URL generation
- ✅ Status checking

## Environment Variables

### Backend (.env)

```bash
# AllPay Configuration
ALLPAY_LOGIN=your_allpay_login
ALLPAY_API_KEY=your_allpay_api_key
ALLPAY_CURRENCY=ILS
ALLPAY_LANGUAGE=AUTO
ALLPAY_TEST_MODE=true
ALLPAY_IFRAME_MODE=true

# URLs for AllPay callbacks
MEDUSA_BACKEND_URL=http://localhost:9000
FRONTEND_URL=http://localhost:5173
```

### Frontend (.env)

```bash
# AllPay Frontend Configuration
VITE_ALLPAY_IFRAME_MODE=true
```

## Configuration Options

### AllPay Provider Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `login` | string | **required** | Your AllPay login from API Integrations |
| `api_key` | string | **required** | Your AllPay API key from API Integrations |
| `currency` | string | `"ILS"` | Default currency (ILS, USD, EUR) |
| `language` | string | `"AUTO"` | Payment page language (AUTO, EN, HE, RU, AR) |
| `test_mode` | boolean | `false` | Enable test mode |
| `iframe_mode` | boolean | `true` | Use iframe integration vs redirect |

## API Endpoints

### Store API (Customer-facing)

- `POST /store/allpay/payment-url` - Generate payment URL
- `POST /store/allpay/payment-status` - Check payment status

### Webhook API

- `POST /webhooks/allpay` - Handle AllPay webhooks

## Payment Flow

### 1. Payment Initiation
1. Customer selects AllPay as payment method
2. Frontend calls MedusaJS payment session API
3. AllPay provider creates payment request
4. AllPay API returns payment URL
5. Customer is presented with iframe or redirect option

### 2. Payment Processing
1. Customer enters payment details on AllPay page
2. AllPay processes the payment
3. AllPay sends webhook notification to backend
4. Backend verifies webhook signature
5. Payment status is updated in MedusaJS

### 3. Payment Completion
1. Customer is redirected to success page
2. Frontend verifies payment status
3. Order is completed in MedusaJS

## Webhook Handling

AllPay sends webhooks for the following events:
- **Status 1**: Successful payment
- **Status 0**: Failed/pending payment  
- **Status 3**: Refunded payment
- **Status 4**: Partially refunded payment

### Webhook Security
- SHA256 signature verification
- IP address validation (optional)
- Automatic retry handling

## Test Cards

For testing in AllPay test mode:

| Card Type | Number | Expiry | CVV | Result |
|-----------|--------|--------|-----|--------|
| Visa | **************** | Any future | Any 3 digits | Success |
| MasterCard | **************** | Any future | Any 3 digits | Success |
| AmEx | *************** | Any future | Any 4 digits | Success |
| Failure | **************** | Any future | Any 3 digits | Failure |

## Iframe Integration

AllPay supports hosted fields for seamless integration:

```html
<iframe 
  src="payment_url_from_allpay"
  allow="payment *"
  frameborder="0"
  scrolling="auto">
</iframe>
```

### Iframe Communication
- Payment success/failure messages via `postMessage`
- Automatic height adjustment
- Mobile-responsive design

## Error Handling

### Common Errors
- **Invalid signature**: Check API key and signature generation
- **Payment URL not generated**: Verify login credentials
- **Webhook verification failed**: Check webhook secret key
- **Payment timeout**: Check expiration settings

### Debugging
- Enable console logging in development
- Use AllPay API Tester for signature verification
- Check webhook delivery in AllPay dashboard

## Security Considerations

1. **API Keys**: Store securely in environment variables
2. **Webhook Verification**: Always verify signatures
3. **HTTPS**: Use HTTPS for all AllPay communications
4. **IP Filtering**: Consider IP-based webhook filtering
5. **Amount Validation**: Verify payment amounts server-side

## Deployment Checklist

- [ ] Set production AllPay credentials
- [ ] Configure webhook URLs
- [ ] Test payment flow end-to-end
- [ ] Verify webhook delivery
- [ ] Test refund functionality
- [ ] Enable production mode (`ALLPAY_TEST_MODE=false`)

## Support

- **AllPay Documentation**: https://allpay.co.il/en/api-reference
- **AllPay Support**: <EMAIL>
- **AllPay API Tester**: Available in AllPay dashboard

## Troubleshooting

### Payment URL Generation Issues
1. Check AllPay credentials
2. Verify signature generation
3. Check API endpoint availability
4. Review request parameters

### Webhook Issues
1. Verify webhook URL accessibility
2. Check signature verification
3. Review webhook payload format
4. Test with AllPay API Tester

### Frontend Integration Issues
1. Check iframe permissions
2. Verify CORS settings
3. Test payment flow in different browsers
4. Check console for JavaScript errors
