import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import { MedusaError } from "@medusajs/framework/utils"

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const body = req.body as { payment_collection_id?: string, cart_id?: string, amount?: number, currency?: string }
    const { payment_collection_id, cart_id, amount, currency } = body

    if (!payment_collection_id && !cart_id) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "payment_collection_id or cart_id is required"
      )
    }

    console.log('🔄 Generating Tranzila payment URL for:', { payment_collection_id, cart_id, amount, currency })

    // Generate a unique transaction ID
    const transactionId = `${payment_collection_id || cart_id}_${Date.now()}`

    // Create payment data for Tranzila
    const paymentData = {
      terminal_name: process.env.TRANZILA_TERMINAL_NAME || "your_terminal_name",
      amount: amount ? (amount).toFixed(2) : "100.00", // Convert from cents or use default
      currency_code: currency || process.env.TRANZILA_CURRENCY || "ILS",
      cred_type: "1", // Credit card
      tranmode: "A", // Authorization
      myid: transactionId,
      response: "1", // Return response data

      // Set URLs for redirect flow
      success_url: `${process.env.FRONTEND_URL || "http://localhost:5173"}/checkout/tranzila/success`,
      error_url: `${process.env.FRONTEND_URL || "http://localhost:5173"}/checkout/tranzila/error`,
      notify_url: `${process.env.MEDUSA_BACKEND_URL || "http://localhost:9000"}/webhooks/tranzila`,

      // Configure iframe mode if enabled
      iframe: process.env.TRANZILA_IFRAME_MODE === "true" ? "1" : "0"
    }

    console.log('🔧 Generated payment data:', { ...paymentData, api_key: '[HIDDEN]' })

    // Generate payment URL based on mode
    let paymentUrl: string

    if (process.env.TRANZILA_IFRAME_MODE === "true") {
      // For iframe mode, use direct.tranzila.com/terminalName/iframenew.php
      const terminalName = process.env.TRANZILA_TERMINAL_NAME || "vadimg"
      const params = new URLSearchParams()

      // Add iframe-specific parameters
      params.append('sum', paymentData.amount)
      params.append('currency', paymentData.currency_code)
      params.append('cred_type', paymentData.cred_type)
      params.append('tranmode', paymentData.tranmode)
      params.append('myid', paymentData.myid)
      params.append('response', paymentData.response)

      paymentUrl = `https://direct.tranzila.com/${terminalName}/iframenew.php?${params.toString()}`
    } else {
      // For redirect mode, use pay.tranzila.com/terminal
      const params = new URLSearchParams()
      Object.entries(paymentData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString())
        }
      })
      paymentUrl = `https://pay.tranzila.com/terminal?${params.toString()}`
    }

    console.log('✅ Generated Tranzila payment URL:', paymentUrl)

    res.json({
      payment_url: paymentUrl,
      session_id: payment_collection_id,
      transaction_id: transactionId,
    })
  } catch (error) {
    console.error("Error generating Tranzila payment URL:", error)
    
    if (error instanceof MedusaError) {
      return res.status(error.type === MedusaError.Types.NOT_FOUND ? 404 : 400).json({
        error: error.message,
        code: error.code,
      })
    }

    res.status(500).json({
      error: "Failed to generate payment URL",
      details: error instanceof Error ? error.message : "Unknown error",
    })
  }
}
