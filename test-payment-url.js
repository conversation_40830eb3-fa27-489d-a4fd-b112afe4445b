// Simple test script to verify Tranzila payment URL generation
const fetch = require('node-fetch');

async function testPaymentUrlGeneration() {
  console.log('🧪 Testing Tranzila payment URL generation...');
  
  try {
    const response = await fetch('http://localhost:9000/store/tranzila/payment-url', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        payment_collection_id: 'test_pc_123',
        cart_id: 'test_cart_456',
        amount: 10000, // 100.00 in cents
        currency: 'ILS'
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('✅ Payment URL generated successfully!');
    console.log('📋 Response:', JSON.stringify(data, null, 2));
    
    // Verify the URL structure
    if (data.payment_url && data.payment_url.includes('localhost:3001')) {
      console.log('✅ URL points to mock server correctly');
    }
    
    if (data.transaction_id) {
      console.log('✅ Transaction ID generated:', data.transaction_id);
    }
    
    console.log('\n🎯 Test completed successfully!');
    console.log('You can now test the full payment flow in your frontend.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure backend is running on port 9000');
    console.log('2. Check that PAYMENT_PROVIDER=tranzila in .env');
    console.log('3. Verify mock server is running on port 3001');
  }
}

// Run the test
testPaymentUrlGeneration();
