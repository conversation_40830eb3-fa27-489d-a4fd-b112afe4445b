<script lang="ts">
  import { onMount, onDestroy } from 'svelte'
  import { browser } from '$app/environment'
  import { detectPDFSource, getEffectivePDFUrl } from '$lib/config/pdf-sources'
  
  export let pdfUrl: string = ''
  export let title: string = 'PDF Document'
  export let height: string = '600px'
  export let initialPage: number = 1
  export let initialZoom: string = 'page-width'
  export let showToolbar: boolean = true
  export let enableSearch: boolean = true
  export let enableDownload: boolean = true
  export let enablePrint: boolean = true
  export let enableCorsProxy: boolean = true // Enable CORS proxy for external sources
  export let corsProxyUrl: string = '' // Custom CORS proxy URL

  let pdfContainer: HTMLDivElement
  let pdfViewer: any = null
  let isLoading = true
  let error = ''
  let currentPage = initialPage
  let totalPages = 0
  let zoomLevel = initialZoom

  // PDF.js instance and document cache
  let pdfjsLib: any = null
  let pdfDocument: any = null // Cache the loaded PDF document
  let isPageLoading = false // Track individual page loading

  // CORS proxy options
  const DEFAULT_CORS_PROXIES = [
    'https://cors-anywhere.herokuapp.com/',
    'https://api.allorigins.win/raw?url=',
    'https://corsproxy.io/?'
  ]

  // Function to get the best URL to use (with or without proxy)
  function getEffectiveUrl(url: string): string {
    if (!enableCorsProxy) return url

    const source = detectPDFSource(url)
    console.log('📄 Detected PDF source:', source.name, 'for URL:', url)

    return getEffectivePDFUrl(url, corsProxyUrl)
  }

  // Function to determine if URL needs CORS proxy (for fallback logic)
  function needsCorsProxy(url: string): boolean {
    const source = detectPDFSource(url)
    return source.requiresCorsProxy && enableCorsProxy
  }

  onMount(async () => {
    if (!browser || !pdfUrl) return

    try {
      // Import PDF.js
      pdfjsLib = await import('pdfjs-dist')

      // Set worker source to match the installed version
      pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/5.4.54/pdf.worker.min.mjs`

      await loadPDF()
    } catch (err) {
      console.error('❌ Failed to initialize PDF.js:', err)
      error = 'Failed to load PDF viewer'
      isLoading = false
    }
  })

  async function loadPDF() {
    if (!pdfjsLib || !pdfUrl) return

    try {
      isLoading = true
      error = ''

      // Get the effective URL (with proxy if needed)
      const effectiveUrl = getEffectiveUrl(pdfUrl)
      console.log('📄 Loading PDF from:', effectiveUrl)

      // Load the PDF document with CORS configuration
      const loadingTask = pdfjsLib.getDocument({
        url: effectiveUrl,
        withCredentials: false, // Don't send cookies for CORS requests
        httpHeaders: {
          'Accept': 'application/pdf,*/*'
        }
      })

      // Cache the PDF document
      pdfDocument = await loadingTask.promise
      totalPages = pdfDocument.numPages
      console.log('📄 PDF loaded successfully. Pages:', totalPages)

      // Render the first page
      await renderCurrentPage()

      isLoading = false
    } catch (err) {
      console.error('❌ Failed to load PDF:', err)

      // If we used a proxy and it failed, try without proxy
      if (needsCorsProxy(pdfUrl) && enableCorsProxy) {
        console.log('🔄 Retrying without CORS proxy...')
        try {
          const directLoadingTask = pdfjsLib.getDocument({
            url: pdfUrl,
            withCredentials: false,
            httpHeaders: {
              'Accept': 'application/pdf,*/*'
            }
          })
          pdfDocument = await directLoadingTask.promise
          totalPages = pdfDocument.numPages
          await renderCurrentPage()
          isLoading = false
          console.log('✅ PDF loaded successfully without proxy')
          return
        } catch (directErr) {
          console.error('❌ Direct load also failed:', directErr)
        }
      }

      error = 'Failed to load PDF document. This may be due to CORS restrictions.'
      isLoading = false
    }
  }

  // Render the current page using the cached PDF document
  async function renderCurrentPage() {
    if (!pdfDocument) return

    isPageLoading = true
    try {
      await renderPage(pdfDocument, currentPage)
    } finally {
      isPageLoading = false
    }
  }

  async function renderPage(pdf: any, pageNumber: number) {
    try {
      const page = await pdf.getPage(pageNumber)
      const scale = getScaleForZoom()
      const viewport = page.getViewport({ scale })

      // Clear previous content
      if (pdfContainer) {
        pdfContainer.innerHTML = ''
      }

      // Create page container
      const pageContainer = document.createElement('div')
      pageContainer.style.position = 'relative'
      pageContainer.style.width = `${viewport.width}px`
      pageContainer.style.height = `${viewport.height}px`

      // Create canvas
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      canvas.height = viewport.height
      canvas.width = viewport.width

      // Add canvas to page container
      pageContainer.appendChild(canvas)

      // Render PDF page into canvas context
      const renderContext = {
        canvasContext: context,
        viewport: viewport
      }

      await page.render(renderContext).promise

      // Add clickable links
      await renderLinks(page, pageContainer, viewport, pdf)

      // Add page container to main container
      if (pdfContainer) {
        pdfContainer.appendChild(pageContainer)
      }

      console.log('📄 Page rendered:', pageNumber)
    } catch (err) {
      console.error('❌ Failed to render page:', err)
      error = 'Failed to render PDF page'
    }
  }

  async function renderLinks(page: any, pageContainer: HTMLElement, viewport: any, pdf: any) {
    try {
      const annotations = await page.getAnnotations()

      annotations.forEach((annotation: any) => {
        if (annotation.subtype === 'Link') {
          const linkElement = document.createElement('a')

          // Position the link overlay
          const rect = annotation.rect
          const transform = viewport.transform
          const x = transform[0] * rect[0] + transform[2] * rect[1] + transform[4]
          const y = transform[1] * rect[0] + transform[3] * rect[1] + transform[5]
          const width = transform[0] * (rect[2] - rect[0])
          const height = transform[3] * (rect[1] - rect[3])

          linkElement.style.position = 'absolute'
          linkElement.style.left = `${x}px`
          linkElement.style.top = `${y - height}px`
          linkElement.style.width = `${width}px`
          linkElement.style.height = `${height}px`
          linkElement.style.cursor = 'pointer'
          linkElement.style.backgroundColor = 'rgba(0, 0, 255, 0.01)' // Optional: visual feedback
          linkElement.style.border = '1px solid rgba(0, 0, 255, 0.3)' // Optional: visual feedback

          // Handle different link types
          if (annotation.url) {
            // External URL
            linkElement.href = annotation.url
            linkElement.target = '_blank'
            linkElement.rel = 'noopener noreferrer'
            linkElement.title = `Open: ${annotation.url}`
          } else if (annotation.dest) {
            // Internal link (page reference)
            linkElement.addEventListener('click', async (e) => {
              e.preventDefault()
              try {
                const dest = await pdf.getDestination(annotation.dest)
                console.log('📄 Navigating to internal link:', dest);
                if (dest && dest[0]) {
                  const pageRef = dest[0]
                  const pageIndex = await pdf.getPageIndex(pageRef)
                  goToPage(pageIndex + 1) // PDF.js uses 0-based, we use 1-based
                }
              } catch (err) {
                console.error('❌ Failed to navigate to internal link:', err)
              }
            })
            linkElement.title = 'Go to page'
            linkElement.style.cursor = 'pointer'
          }

          pageContainer.appendChild(linkElement)
        }
      })
    } catch (err) {
      console.error('❌ Failed to render links:', err)
    }
  }

  function getScaleForZoom(): number {
    switch (zoomLevel) {
      case 'page-width':
        return 1.0 // Will be calculated based on container width
      case 'page-height':
        return 1.2
      case 'page-fit':
        return 0.8
      case 'auto':
        return 1.0
      default:
        return parseFloat(zoomLevel) || 1.0
    }
  }

  async function goToPage(pageNum: number) {
    if (!pdfDocument || pageNum < 1 || pageNum > totalPages || isPageLoading) return

    currentPage = pageNum
    console.log('📄 Navigating to page:', pageNum)

    await renderCurrentPage()
  }

  async function changeZoom(newZoom: string) {
    if (!pdfDocument || isPageLoading) return

    zoomLevel = newZoom
    console.log('📄 Changing zoom to:', newZoom)

    await renderCurrentPage()
  }

  function nextPage() {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1)
    }
  }

  function prevPage() {
    if (currentPage > 1) {
      goToPage(currentPage - 1)
    }
  }

  function toPage(num: number) {
    if (num >= 1 && num <= totalPages) {
      goToPage(num)
    }
  }

  onDestroy(() => {
    // Cleanup PDF document and viewer
    if (pdfDocument) {
      pdfDocument.destroy()
      pdfDocument = null
    }
    pdfViewer = null
    pdfjsLib = null
  })

  // Reactive statements
  $: if (pdfUrl && pdfjsLib) {
    loadPDF()
  }
</script>

<div class="pdf-viewer-container" style="height: {height};">
  <!-- PDF Toolbar -->
  {#if showToolbar}
    <div class="pdf-toolbar" style="background: var(--color-bg-secondary); border-bottom: 1px solid var(--color-border); ">
      <div class="flex items-center justify-between">
        <!-- Navigation Controls -->
        <div class="flex items-center gap-2">
          <button
            on:click={prevPage}
            disabled={currentPage <= 1 || isLoading || isPageLoading}
            class="pdf-btn"
            title="Previous Page"
          >
            {#if isPageLoading && currentPage > 1}
              ⏳
            {:else}
              ←
            {/if}
          </button>

          <span class="text-sm" style="color: var(--color-text-secondary);">
            Page {currentPage} of {totalPages}
            {#if isPageLoading}
              <span class="text-xs">(loading...)</span>
            {/if}
          </span>

          <button
            on:click={nextPage}
            disabled={currentPage >= totalPages || isLoading || isPageLoading}
            class="pdf-btn"
            title="Next Page"
          >
            {#if isPageLoading && currentPage < totalPages}
              ⏳
            {:else}
              →
            {/if}
          </button>
        </div>


        <div class="flex items-center gap-2">
          <span class="text-sm" style="color: var(--color-text-secondary);">
            <input type="number" 
            bind:value={currentPage} 
            disabled={isLoading || isPageLoading} 
      class="w-full px-3 py-2 font-book-text border rounded-md focus:outline-none focus:ring-2 transition-colors"
      style="border-color: 'var(--color-border)'}; background: var(--color-bg-primary); color: var(--color-text-primary); focus:ring-color: var(--color-primary);"
            on:change={() => toPage(currentPage)} />
          </span>
          <button
            on:click={() => toPage(currentPage)}
            disabled={totalPages <= 1 || isLoading || isPageLoading}
            class="pdf-btn"
            title="Previous Page"
          >
            {#if isPageLoading || isLoading }
              ⏳
            {:else}
              Go
            {/if}
          </button>

        </div>


        <!-- Zoom Controls -->
        <div class="flex items-center gap-2">
          <select
            bind:value={zoomLevel}
            on:change={() => changeZoom(zoomLevel)}
            class="pdf-select"
            disabled={isLoading}
          >
            <option value="page-width">Fit Width</option>
            <option value="page-height">Fit Height</option>
            <option value="page-fit">Fit Page</option>
            <option value="0.5">50%</option>
            <option value="0.75">75%</option>
            <option value="1.0">100%</option>
            <option value="1.25">125%</option>
            <option value="1.5">150%</option>
            <option value="2.0">200%</option>
          </select>
        </div>

      </div>
    </div>
  {/if}

  <!-- PDF Content -->
  <div class="pdf-content" style="height: calc(100% - {showToolbar ? '48px' : '0px'}); overflow: auto;">
    {#if isLoading}
      <div class="pdf-loading">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" style="border-color: var(--color-primary);"></div>
        <p style="color: var(--color-text-secondary);">Loading PDF...</p>
      </div>
    {:else if error}
      <div class="pdf-error">
        <p style="color: var(--color-error);">❌ {error}</p>
        <button on:click={loadPDF} class="pdf-btn mt-2">Retry</button>
      </div>
    {:else}
      <div
        bind:this={pdfContainer}
        class="pdf-container"
        style="display: flex; justify-content: center;"
      ></div>
    {/if}
  </div>
</div>

<style>
  .pdf-viewer-container {
    border: 1px solid var(--color-border);
    border-radius: 8px;
    overflow: hidden;
    background: var(--color-bg-primary);
  }

  .pdf-toolbar {
    border-bottom: 1px solid var(--color-border);
  }

  .pdf-btn {
    padding: 4px 8px;
    border: 1px solid var(--color-border);
    background: var(--color-bg-primary);
    color: var(--color-text-primary);
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
  }

  .pdf-btn:hover:not(:disabled) {
    background: var(--color-bg-secondary);
    border-color: var(--color-primary);
  }

  .pdf-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .pdf-select {
    padding: 4px 8px;
    border: 1px solid var(--color-border);
    background: var(--color-bg-primary);
    color: var(--color-text-primary);
    border-radius: 4px;
    font-size: 14px;
  }

  .pdf-loading,
  .pdf-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
  }

  .pdf-container {
    min-height: 100%;
  }

  .pdf-container canvas {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
  }

  /* Link overlay styles */
  .pdf-container a {
    transition: background-color 0.2s ease;
  }

  .pdf-container a:hover {
    background-color: rgba(0, 0, 255, 0.2) !important;
  }
</style>
