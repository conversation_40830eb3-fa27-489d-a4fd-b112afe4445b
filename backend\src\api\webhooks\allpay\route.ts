import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  console.log('🔔 ALLPAY WEBHOOK RECEIVED')
  console.log('⏰ Timestamp:', new Date().toISOString())
  console.log('🌐 Request Headers:', JSON.stringify({
    'content-type': req.headers['content-type'],
    'user-agent': req.headers['user-agent']
  }, null, 2))

  try {
    const webhookData = req.body as {
      order_id: string
      amount: number
      status: number
      client_name?: string
      client_email?: string
      client_tehudat?: string
      foreign_card: number
      card_mask?: string
      card_brand?: string
      currency: string
      receipt?: string
      add_field_1?: string
      add_field_2?: string
      sign: string
    }

    console.log('📦 Webhook Data:', JSON.stringify(webhookData, null, 2))

    // Verify webhook signature
    const isValidSignature = verifyAllPaySignature(webhookData, process.env.ALLPAY_API_KEY || "your_api_key")
    
    if (!isValidSignature) {
      console.error('❌ Invalid AllPay webhook signature')
      return res.status(400).json({ error: 'Invalid signature' })
    }

    console.log('✅ AllPay webhook signature verified')

    // Process webhook based on payment status
    switch (webhookData.status) {
      case 1: // Successful payment
        console.log('💳 Processing successful AllPay payment')
        await handlePaymentSuccess(webhookData)
        break
      
      case 0: // Failed/pending payment
        console.log('❌ Processing failed AllPay payment')
        await handlePaymentFailure(webhookData)
        break
      
      case 3: // Refunded payment
        console.log('🔄 Processing AllPay refund')
        await handlePaymentRefund(webhookData)
        break
      
      case 4: // Partially refunded payment
        console.log('🔄 Processing AllPay partial refund')
        await handlePaymentPartialRefund(webhookData)
        break
      
      default:
        console.log(`⚠️ Unknown AllPay payment status: ${webhookData.status}`)
        break
    }

    // Respond with success to acknowledge webhook receipt
    res.status(200).json({ 
      received: true,
      order_id: webhookData.order_id,
      status: webhookData.status
    })

  } catch (error) {
    console.error('❌ AllPay webhook processing error:', error)
    res.status(500).json({ 
      error: 'Webhook processing failed',
      message: error.message
    })
  }
}

/**
 * Verify AllPay webhook signature
 */
function verifyAllPaySignature(data: Record<string, any>, apiKey: string): boolean {
  try {
    const crypto = require('crypto')
    
    // Remove sign parameter
    const { sign, ...dataWithoutSign } = data
    
    // Sort keys alphabetically
    const sortedKeys = Object.keys(dataWithoutSign).sort()
    const chunks: string[] = []

    sortedKeys.forEach((key) => {
      const value = dataWithoutSign[key]
      if (value !== null && value !== undefined && String(value).trim() !== '') {
        chunks.push(String(value).trim())
      }
    })

    // Create signature string
    const signatureString = chunks.join(':') + ':' + apiKey
    
    // Generate SHA256 hash
    const expectedSignature = crypto.createHash('sha256').update(signatureString).digest('hex')
    
    return expectedSignature === sign
  } catch (error) {
    console.error('❌ AllPay signature verification error:', error)
    return false
  }
}

/**
 * Handle successful payment
 */
async function handlePaymentSuccess(webhookData: any) {
  console.log('✅ AllPay payment succeeded:', webhookData.order_id)
  
  // Here you would typically:
  // 1. Update payment status in database
  // 2. Update order status
  // 3. Send confirmation email to customer
  // 4. Trigger fulfillment process
  
  console.log(`💰 Payment ${webhookData.order_id} for amount ${webhookData.amount} ${webhookData.currency} succeeded`)
  console.log(`💳 Card: ${webhookData.card_brand} ending in ${webhookData.card_mask?.slice(-4)}`)
  console.log(`👤 Customer: ${webhookData.client_name} (${webhookData.client_email})`)
  
  if (webhookData.receipt) {
    console.log(`🧾 Receipt: ${webhookData.receipt}`)
  }
}

/**
 * Handle failed payment
 */
async function handlePaymentFailure(webhookData: any) {
  console.log('❌ AllPay payment failed:', webhookData.order_id)
  
  // Here you would typically:
  // 1. Update payment status in database
  // 2. Send failure notification to customer
  // 3. Log the failure for analysis
  
  console.log(`💸 Payment ${webhookData.order_id} for amount ${webhookData.amount} ${webhookData.currency} failed`)
}

/**
 * Handle refunded payment
 */
async function handlePaymentRefund(webhookData: any) {
  console.log('🔄 AllPay payment refunded:', webhookData.order_id)
  
  // Here you would typically:
  // 1. Update payment status in database
  // 2. Update order status
  // 3. Send refund confirmation to customer
  // 4. Handle inventory adjustments
  
  console.log(`💰 Payment ${webhookData.order_id} for amount ${webhookData.amount} ${webhookData.currency} refunded`)
}

/**
 * Handle partially refunded payment
 */
async function handlePaymentPartialRefund(webhookData: any) {
  console.log('🔄 AllPay payment partially refunded:', webhookData.order_id)
  
  // Here you would typically:
  // 1. Update payment status in database
  // 2. Update order status
  // 3. Send partial refund confirmation to customer
  // 4. Handle partial inventory adjustments
  
  console.log(`💰 Payment ${webhookData.order_id} for amount ${webhookData.amount} ${webhookData.currency} partially refunded`)
}
