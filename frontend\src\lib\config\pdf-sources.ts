// PDF Sources Configuration
// This file manages different PDF sources and their CORS requirements

export interface PDFSource {
  name: string
  baseUrl: string
  requiresCorsProxy: boolean
  corsProxyUrl?: string
  headers?: Record<string, string>
}

export const PDF_SOURCES: PDFSource[] = [
  {
    name: 'Local Backend',
    baseUrl: 'http://localhost:9000',
    requiresCorsProxy: false
  },
  {
    name: 'Production Backend',
    baseUrl: 'https://hebrewbook-medusa-backend.azurewebsites.net',
    requiresCorsProxy: false
  },
  {
    name: 'Google Drive',
    baseUrl: 'https://drive.google.com',
    requiresCorsProxy: true,
    corsProxyUrl: 'https://corsproxy.io/?'
  },
  {
    name: 'Dropbox',
    baseUrl: 'https://dl.dropboxusercontent.com',
    requiresCorsProxy: true,
    corsProxyUrl: 'https://corsproxy.io/?'
  },
  {
    name: 'Archive.org',
    baseUrl: 'https://archive.org',
    requiresCorsProxy: false // Archive.org usually allows CORS
  },
  {
    name: 'GitHub Raw',
    baseUrl: 'https://raw.githubusercontent.com',
    requiresCorsProxy: false // GitHub raw files allow CORS
  },
  {
    name: 'Generic External',
    baseUrl: '',
    requiresCorsProxy: true,
    corsProxyUrl: 'https://corsproxy.io/?'
  }
]

// Function to detect which source a URL belongs to
export function detectPDFSource(url: string): PDFSource {
  for (const source of PDF_SOURCES) {
    if (source.baseUrl && url.startsWith(source.baseUrl)) {
      return source
    }
  }
  
  // Default to generic external source
  return PDF_SOURCES[PDF_SOURCES.length - 1]
}

// Function to get the effective URL with proxy if needed
export function getEffectivePDFUrl(url: string, customProxy?: string): string {
  const source = detectPDFSource(url)
  
  if (!source.requiresCorsProxy) {
    return url
  }
  
  const proxyUrl = customProxy || source.corsProxyUrl || 'https://corsproxy.io/?'
  return proxyUrl + encodeURIComponent(url)
}

// Common PDF hosting services and their characteristics
export const PDF_HOSTING_SERVICES = {
  'Google Drive': {
    pattern: /drive\.google\.com/,
    corsSupport: false,
    notes: 'Requires CORS proxy. Use direct download links.'
  },
  'Dropbox': {
    pattern: /dropbox(usercontent)?\.com/,
    corsSupport: false,
    notes: 'Use dl.dropboxusercontent.com URLs with CORS proxy.'
  },
  'OneDrive': {
    pattern: /1drv\.ms|onedrive\.live\.com/,
    corsSupport: false,
    notes: 'Requires CORS proxy and proper sharing settings.'
  },
  'GitHub': {
    pattern: /raw\.githubusercontent\.com/,
    corsSupport: true,
    notes: 'Works directly without proxy.'
  },
  'Archive.org': {
    pattern: /archive\.org/,
    corsSupport: true,
    notes: 'Usually works directly without proxy.'
  },
  'AWS S3': {
    pattern: /s3\.amazonaws\.com|\.s3\./,
    corsSupport: 'depends',
    notes: 'Depends on bucket CORS configuration.'
  }
}
