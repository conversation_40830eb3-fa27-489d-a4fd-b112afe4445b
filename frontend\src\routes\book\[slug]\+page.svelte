<script lang="ts">
	import { onMount } from 'svelte'
	import { page } from '$app/stores'
	import { _ } from '$lib/i18n'
	import { getCurrentLocale, isRTL } from '$lib/i18n'
	import { productsStore, productActions } from '$lib/stores/products'
	import { chaptersStore } from '$lib/stores/chapters'
	import { cartActions, cartLoading } from '$lib/stores/cart'
	import { formatPriceWithCurrency } from '$lib/stores/currency'
	import type { Product } from '$lib/types/products'
	import type { Chapter } from '$lib/stores/chapters'
	import { goto } from '$app/navigation'

	let currentLocale = 'en'
	let rtl = false
	let book: Product | null = null
	let imageLoading = true
	let imageError = false
	let addingChapterToCart: string | null = null

	// Pagination variables
	let currentPage = 1
	const chaptersPerPage = 4

	$: currentLocale = getCurrentLocale()
	$: rtl = isRTL(currentLocale)
	$: allProducts = $productsStore
	$: chapters = $chaptersStore.chapters
	$: isLoadingChapters = $chaptersStore.isLoading
	$: slug = $page.params.slug

	// Pagination calculations
	$: totalPages = Math.ceil(chapters.length / chaptersPerPage)
	$: startIndex = (currentPage - 1) * chaptersPerPage
	$: endIndex = startIndex + chaptersPerPage
	$: paginatedChapters = chapters.slice(startIndex, endIndex)

	// Reset to first page when chapters change
	$: if (chapters.length > 0) {
		if (currentPage > totalPages) {
			currentPage = 1
		}
	}

	// Find book by slug (could be ID or title-based slug)
	$: if (allProducts.length > 0 && slug) {
		book = allProducts.find(product => 
			product.id === slug || 
			product.handle === slug ||
			product.title.toLowerCase().replace(/\s+/g, '-') === slug.toLowerCase()
		) || null
		
		if (book) {
			console.log('📖 Found book:', book.title)
			chaptersStore.loadChaptersForBook(book)
		}
	}

	onMount(async () => {
		// Load products if not already loaded
		if (allProducts.length === 0) {
			console.log('📚 Loading products for book page...')
			await productActions.loadProducts()
		}
	})

	function handleImageLoad() {
		imageLoading = false
		imageError = false
	}

	function handleImageError() {
		imageLoading = false
		imageError = true
	}

	function handleChapterPreview(chapterId: string) {
		goto(`/chapters/${chapterId}`)
	}

	async function handleAddChapterToCart(chapter: Chapter) {
		if (!book) return
		
		addingChapterToCart = chapter.id
		try {
			// Create a product-like object for the chapter
			const chapterProduct = {
				id: `chapter-${chapter.id}`,
				title: chapter.title,
				price: chapter.price || 0,
				type: 'chapter',
				chapter_id: chapter.id,
				book_id: book.id
			}
			
			await cartActions.addItem(chapterProduct as any, 1)
			console.log('✅ Chapter added to cart:', chapter.title)
		} catch (error) {
			console.error('❌ Failed to add chapter to cart:', error)
		} finally {
			addingChapterToCart = null
		}
	}

	function getLocalizedTitle(chapter: Chapter): string {
		switch (currentLocale) {
			case 'he':
				return chapter.title
			case 'en':
				return chapter.title || chapter.title
			case 'ru':
				return chapter.title || chapter.title
			default:
				return chapter.title
		}
	}

	// Pagination functions
	function goToPage(page: number) {
		if (page >= 1 && page <= totalPages) {
			currentPage = page
		}
	}

	function nextPage() {
		if (currentPage < totalPages) {
			currentPage++
		}
	}

	function prevPage() {
		if (currentPage > 1) {
			currentPage--
		}
	}


</script>

<svelte:head>
	<title>{book ? book.title : $_('common.loading')} - Hebrew Book Store</title>
	<meta name="description" content={book ? book.description : $_('book.page_description')} />
</svelte:head>

<div class="min-h-screen" style="background: var(--color-bg-primary);" class:rtl>
	{#if !book}
		<!-- Loading State -->
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
			<div class="text-center">
				<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-accent mx-auto mb-4"></div>
				<p style="color: var(--color-text-secondary);">{$_('common.loading')}</p>
			</div>
		</div>
	{:else}
		<!-- Book Detail Page -->
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
			<div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
				
				<!-- Left: Book Image -->
				<div class="p-8 flex items-center justify-center">
					<div class="relative max-w-md w-full">
						{#if imageLoading}
							<div class="aspect-[3/4] bg-gray-200 rounded-lg animate-pulse flex items-center justify-center">
								<div class="text-gray-400">
									<svg class="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
										<path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
									</svg>
								</div>
							</div>
						{/if}
						
						{#if book.cover_image && !imageError}
							<img
								src={book.cover_image}
								alt={book.title}
								class="w-full h-full object-cover transition-opacity duration-300"
								class:hidden={imageLoading}
								on:load={handleImageLoad}
								on:error={handleImageError}
							/>
						{:else if !imageLoading}
							<!-- Fallback book icon -->
							<div class="aspect-[3/4] flex items-center justify-center rounded-lg" style="background: var(--color-bg-primary); border: 2px solid var(--color-border);">
								<div class="text-center" style="color: var(--color-text-secondary);">
									<svg class="w-16 h-16 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
										<path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"/>
									</svg>
									<p class="text-sm font-medium">{$_('common.book_cover')}</p>
								</div>
							</div>
						{/if}
					</div>
				</div>

				<!-- Right: Book Details -->
				<div class="space-y-8">
					<!-- Book Title -->
					<div>
						<h1 class="font-title text-3xl md:text-4xl font-bold mb-4" style="color: var(--color-text-accent);">
							{book.title}
						</h1>
						{#if book.description}
							<p class="font-body text-lg leading-relaxed" style="color: var(--color-text-secondary);">
								{book.description}
							</p>
						{/if}
					</div>

					<!-- Table of Contents -->
					<div>
						<h2 class="font-title text-2xl font-bold mb-6" style="color: var(--color-text-primary);">
							{$_('book.table_of_contents')}
						</h2>

						{#if isLoadingChapters}
							<div class="space-y-4">
								{#each Array(3) as _}
									<div class="animate-pulse">
										<div class="h-20 bg-gray-200 rounded-lg"></div>
									</div>
								{/each}
							</div>
						{:else if chapters.length > 0}
							<div class="space-y-2">
								{#each paginatedChapters as chapter}
									<div class="p-0 hover:shadow-md transition-shadow duration-300">
										<div class="flex items-start justify-between gap-4">
												<h3 class="font-title text-lg font-semibold mb-2" style="color: var(--color-text-primary);">
													{getLocalizedTitle(chapter)}
												</h3>

											<div class="flex flex-row gap-2 min-w-[120px]">
												<!-- Preview Button -->
												<button
													class="btn-light-brown text-sm px-4 py-2"
													on:click={() => handleChapterPreview(chapter.id)}
												>
													{$_('book.preview')}
												</button>

												<!-- Add to Cart Button -->
												<button
													class="btn-blue text-sm px-4 py-2"
													on:click={() => handleAddChapterToCart(chapter)}
													disabled={addingChapterToCart === chapter.id || $cartLoading}
													class:opacity-50={addingChapterToCart === chapter.id}
												>
													{#if addingChapterToCart === chapter.id}
														{$_('common.adding')}...
													{:else}
														{$_('book.add_to_cart')} - {chapter.formatted_price || formatPriceWithCurrency(chapter.price || 0)}
													{/if}
												</button>
											</div>
										</div>
									</div>
								{/each}
							</div>

							<!-- Pagination -->
							{#if totalPages > 1}
								<div class="flex justify-center items-center gap-2 mt-6">
									<!-- Previous Button -->
									<button
										class="px-3 py-2 rounded-md border transition-colors duration-200"
										class:opacity-50={currentPage === 1}
										class:cursor-not-allowed={currentPage === 1}
										style="border-color: var(--color-border); background: var(--color-bg-secondary); color: var(--color-text-primary);"
										disabled={currentPage === 1}
										on:click={prevPage}
									>
										‹
									</button>

									<!-- Page Numbers -->
									{#each Array(totalPages) as _, i}
										{@const pageNum = i + 1}
										<button
											class="px-3 py-2 rounded-full transition-colors duration-200"
											class:font-bold={currentPage === pageNum}
											style="border-color: var(--color-border);
												   background: {currentPage === pageNum ? 'var(--color-accent)' : 'var(--color-bg-secondary)'};
												   color: {currentPage === pageNum ? 'white' : 'var(--color-text-primary)'};"
											on:click={() => goToPage(pageNum)}
										>
											{pageNum}
										</button>
									{/each}

									<!-- Next Button -->
									<button
										class="px-3 py-2 rounded-md border transition-colors duration-200"
										class:opacity-50={currentPage === totalPages}
										class:cursor-not-allowed={currentPage === totalPages}
										style="border-color: var(--color-border); background: var(--color-bg-secondary); color: var(--color-text-primary);"
										disabled={currentPage === totalPages}
										on:click={nextPage}
									>
										›
									</button>
								</div>
							{/if}
						{:else}
							<div class="text-center py-8">
								<p style="color: var(--color-text-secondary);">{$_('book.no_chapters')}</p>
							</div>
						{/if}
					</div>
				</div>
			</div>
		</div>
	{/if}
</div>
