import type { MedusaRequest, MedusaResponse } from "@medusajs/framework/http"
import TranzilaPaymentProviderService from "../../../modules/payment-tranzila/service"

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  console.log('🔔 TRANZILA WEBHOOK RECEIVED')
  console.log('⏰ Timestamp:', new Date().toISOString())
  console.log('🌐 Request Headers:', JSON.stringify({
    'content-type': req.headers['content-type'],
    'user-agent': req.headers['user-agent']
  }, null, 2))

  try {
    const responseData = req.body as Record<string, string>
    console.log('📦 Tranzila Response Data:', JSON.stringify(responseData, null, 2))

    // Note: With the new API flow, we get structured responses from Tranzila
    // The handshake mechanism provides anti-fraud protection
    console.log('🔍 Processing Tranzila API callback with handshake validation')

    // Extract transaction details
    const transactionId = responseData.myid
    const responseCode = responseData.Response
    const amount = responseData.sum
    const currency = responseData.currency
    const confirmationCode = responseData.ConfirmationCode
    const responseMessage = responseData.Responsemessage
    const cardType = responseData.cardtype
    const maskedCardNumber = responseData.cardnum
    const expMonth = responseData.expmonth
    const expYear = responseData.expyear

    console.log(`💳 Processing Tranzila transaction: ${transactionId}`)
    console.log(`💰 Amount: ${amount} ${currency}`)
    console.log(`📋 Response: ${responseCode} - ${responseMessage}`)

    // Get payment service using correct resolution
    let paymentSessions: any[] = []

    try {
      const paymentService = req.scope.resolve("paymentService") as any
      paymentSessions = await paymentService.listPaymentSessions({
        data: {
          id: transactionId,
        },
      })
    } catch (error) {
      console.log('paymentService not found, trying alternative resolution...')

      try {
        // Alternative: Query database directly
        const manager = req.scope.resolve("manager") as any
        const paymentSessionRepo = manager.getRepository("PaymentSession")
        paymentSessions = await paymentSessionRepo.find({
          where: {
            data: { id: transactionId }
          }
        })
      } catch (dbError) {
        console.error('Failed to find payment sessions:', dbError)
        return res.status(404).json({ error: "Payment session not found" })
      }
    }

    const paymentSession = paymentSessions.find((session: any) => 
      session.data?.id === transactionId || session.data?.payment_data?.myid === transactionId
    )

    if (!paymentSession) {
      console.error(`❌ Payment session not found for transaction: ${transactionId}`)
      return res.status(404).json({ error: "Payment session not found" })
    }

    // Update payment session based on response
    let newStatus = "error"
    let updateData: any = {
      ...paymentSession.data,
      tranzila_response: responseData,
      updated_at: new Date().toISOString(),
    }

    if (responseCode === "000") {
      // Success
      newStatus = "authorized"
      updateData.status = "authorized"
      updateData.confirmation_code = confirmationCode
      updateData.card_type = cardType
      updateData.masked_card_number = maskedCardNumber
      updateData.authorized_at = new Date().toISOString()
      
      console.log('✅ Payment authorized successfully')
    } else {
      // Error
      newStatus = "error"
      updateData.status = "error"
      updateData.error_code = responseCode
      updateData.error_message = responseMessage
      
      console.log(`❌ Payment failed: ${responseCode} - ${responseMessage}`)
    }

    // Update the payment session
    try {
      const paymentService = req.scope.resolve("paymentService") as any
      await paymentService.updatePaymentSession(paymentSession.id, {
        data: updateData,
      })
    } catch (error) {
      console.log('paymentService not found for update, trying alternative...')

      try {
        // Alternative: Update via database
        const manager = req.scope.resolve("manager") as any
        const paymentSessionRepo = manager.getRepository("PaymentSession")
        await paymentSessionRepo.update(paymentSession.id, {
          data: updateData,
        })
      } catch (dbError) {
        console.error('Failed to update payment session:', dbError)
        // Continue anyway - webhook processing shouldn't fail completely
      }
    }

    // If payment was successful, we might want to trigger order completion
    // This depends on your specific workflow implementation
    if (responseCode === "000") {
      console.log('🎉 Payment successful, order can be completed')
      // You might want to emit an event here or call a workflow
      // Example: await eventBusService.emit("tranzila.payment.authorized", { payment_session: paymentSession })
    }

    console.log('✅ Tranzila webhook processed successfully')
    res.status(200).json({ 
      status: "success",
      message: "Webhook processed successfully" 
    })

  } catch (error) {
    console.error('❌ Error processing Tranzila webhook:', error)
    
    res.status(500).json({
      error: "Failed to process webhook",
      details: error instanceof Error ? error.message : "Unknown error",
    })
  }
}

// Handle GET requests for webhook verification (if Tranzila requires it)
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  console.log('🔍 Tranzila webhook verification request')
  
  // Some payment providers require webhook endpoint verification
  // Return a simple success response
  res.status(200).json({ 
    status: "ok",
    message: "Tranzila webhook endpoint is active" 
  })
}
