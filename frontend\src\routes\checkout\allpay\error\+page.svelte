<script lang="ts">
  import { onMount } from 'svelte'
  import { goto } from '$app/navigation'
  import { page } from '$app/stores'
  import { _ } from '$lib/i18n'

  let errorMessage = ''
  let transactionId = ''

  onMount(() => {
    // Get error details from URL parameters or session storage
    const urlParams = new URLSearchParams(window.location.search)
    errorMessage = urlParams.get('error') || $_('checkout.payment_failed_generic')
    transactionId = urlParams.get('order_id') || sessionStorage.getItem('allpay_transaction_id') || ''

    // Clear session storage
    sessionStorage.removeItem('allpay_cart_id')
    sessionStorage.removeItem('allpay_transaction_id')
  })

  function handleRetryPayment() {
    goto('/checkout')
  }

  function handleReturnToShop() {
    goto('/')
  }

  function handleContactSupport() {
    // You can customize this to open a support chat, email, or contact form
    window.open('mailto:<EMAIL>?subject=Payment Issue&body=Transaction ID: ' + transactionId, '_blank')
  }
</script>

<svelte:head>
  <title>{$_('checkout.payment_failed')} - AllPay</title>
</svelte:head>

<div class="error-page">
  <div class="container">
    <div class="error-state">
      <div class="error-icon">❌</div>
      <h2>{$_('checkout.payment_failed')}</h2>
      <p class="error-message">{errorMessage}</p>
      
      {#if transactionId}
        <div class="transaction-info">
          <p><strong>{$_('checkout.transaction_id')}:</strong> {transactionId}</p>
        </div>
      {/if}

      <div class="error-details">
        <h3>{$_('checkout.what_happened')}</h3>
        <ul>
          <li>{$_('checkout.payment_not_processed')}</li>
          <li>{$_('checkout.no_charge_made')}</li>
          <li>{$_('checkout.can_try_again')}</li>
        </ul>
      </div>

      <div class="suggestions">
        <h3>{$_('checkout.suggestions')}</h3>
        <ul>
          <li>{$_('checkout.check_card_details')}</li>
          <li>{$_('checkout.ensure_sufficient_funds')}</li>
          <li>{$_('checkout.try_different_card')}</li>
          <li>{$_('checkout.contact_bank_if_needed')}</li>
        </ul>
      </div>

      <div class="actions">
        <button on:click={handleRetryPayment} class="btn-primary">
          {$_('checkout.retry_payment')}
        </button>
        <button on:click={handleReturnToShop} class="btn-secondary">
          {$_('checkout.return_to_shop')}
        </button>
        <button on:click={handleContactSupport} class="btn-outline">
          {$_('checkout.contact_support')}
        </button>
      </div>
    </div>
  </div>
</div>

<style>
  .error-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    padding: 2rem;
  }

  .container {
    max-width: 600px;
    width: 100%;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 3rem;
    text-align: center;
  }

  .error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
  }

  .error-icon {
    font-size: 4rem;
    color: #dc3545;
  }

  h2 {
    margin: 0;
    color: #dc3545;
    font-size: 1.8rem;
  }

  .error-message {
    margin: 0;
    color: #666;
    font-size: 1.1rem;
    font-weight: 500;
  }

  .transaction-info {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    margin: 1rem 0;
    font-family: monospace;
    font-size: 0.9rem;
    color: #495057;
  }

  .error-details, .suggestions {
    text-align: left;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1rem 0;
  }

  .error-details h3, .suggestions h3 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1.1rem;
    text-align: center;
  }

  .error-details ul, .suggestions ul {
    margin: 0;
    padding-left: 1.5rem;
    color: #555;
  }

  .error-details li, .suggestions li {
    margin-bottom: 0.5rem;
  }

  .actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 2rem;
  }

  .btn-primary, .btn-secondary, .btn-outline {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-block;
  }

  .btn-primary {
    background-color: #007bff;
    color: white;
  }

  .btn-primary:hover {
    background-color: #0056b3;
  }

  .btn-secondary {
    background-color: #6c757d;
    color: white;
  }

  .btn-secondary:hover {
    background-color: #545b62;
  }

  .btn-outline {
    background-color: transparent;
    color: #007bff;
    border: 2px solid #007bff;
  }

  .btn-outline:hover {
    background-color: #007bff;
    color: white;
  }

  @media (min-width: 768px) {
    .actions {
      flex-direction: row;
      justify-content: center;
    }

    .btn-primary, .btn-secondary, .btn-outline {
      flex: 1;
      max-width: 200px;
    }
  }

  @media (max-width: 767px) {
    .error-page {
      padding: 1rem;
    }

    .container {
      padding: 2rem;
    }
  }
</style>
