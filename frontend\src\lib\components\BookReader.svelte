<script lang="ts">
  import PDFViewer from './PDFViewer.svelte'
  import { _ } from '$lib/i18n'

  export let book: any = null
  export let title: string = ''
  export let height: string = '600px'
  export let isLoading: boolean = false

  // Reader type detection
  $: readerType = detectReaderType(book)
  $: contentUrl = getContentUrl(book)

  function detectReaderType(book: any): 'pdf' | 'fliphtml5' | 'none' {
    if (!book) return 'none'

    const mainMedia = book.medias?.find((media: any) => media.type === 'main')
    const url = mainMedia?.url || book.fliphtml5_url

    if (!url) return 'none'

    // Check if it's a PDF file
    if (url.toLowerCase().includes('.pdf') || url.toLowerCase().includes('pdf')) {
      return 'pdf'
    }

    // Check if it's a FlipHTML5 URL
    if (url.includes('fliphtml5.com') || url.includes('online.fliphtml5')) {
      return 'fliphtml5'
    }

    // Default to FlipHTML5 for backward compatibility
    return 'fliphtml5'
  }

  function getContentUrl(book: any): string {
    if (!book) return ''

    const mainMedia = book.medias?.find((media: any) => media.type === 'main')
    return mainMedia?.url || book.fliphtml5_url || ''
  }

  function getReaderTypeLabel(type: string): string {
    switch (type) {
      case 'pdf':
        return '📄 PDF Reader'
      case 'fliphtml5':
        return '📖 FlipHTML5 Reader'
      default:
        return '📚 Book Reader'
    }
  }
</script>

<div class="book-reader-container" style="height: {height};">
  {#if isLoading}
    <!-- Loading State -->
    <div class="reader-loading">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" style="border-color: var(--color-primary);"></div>
        <p class="font-book-text" style="color: var(--color-text-secondary);">
          {$_('library.loading_reader')}
        </p>
      </div>
    </div>
  {:else if !book || !contentUrl}
    <!-- No Content State -->
    <div class="reader-empty">
      <div class="text-center">
        <div class="text-6xl mb-4">📚</div>
        <h3 class="font-book-title text-lg font-semibold mb-2" style="color: var(--color-text-primary);">
          {$_('library.no_book_selected')}
        </h3>
        <p class="font-book-text" style="color: var(--color-text-secondary);">
          {$_('library.select_book_to_read')}
        </p>
      </div>
    </div>
  {:else}
    <!-- Reader Content -->
    <div class="reader-content" style="height: calc(100% );">
      {#if readerType === 'pdf'}
        <!-- PDF.js Reader -->
        <PDFViewer
          pdfUrl={contentUrl}
          {title}
          height="100%"
          showToolbar={true}
          enableSearch={true}
          enableDownload={true}
          enablePrint={true}
          enableCorsProxy={true}
          corsProxyUrl=""
          initialZoom="page-width"
        />
      {:else if readerType === 'fliphtml5'}
        <!-- FlipHTML5 Reader -->
        <div class="fliphtml5-container">
          <iframe
            src={contentUrl}
            class="w-full h-full border-0 rounded-lg"
            {title}
            allowfullscreen
            loading="lazy"
          ></iframe>
        </div>
      {:else}
        <!-- Unsupported Format -->
        <div class="reader-error">
          <div class="text-center">
            <div class="text-4xl mb-4">⚠️</div>
            <h3 class="font-book-title text-lg font-semibold mb-2" style="color: var(--color-text-primary);">
              Unsupported Format
            </h3>
            <p class="font-book-text" style="color: var(--color-text-secondary);">
              This book format is not supported by the reader.
            </p>
            <p class="text-sm mt-2" style="color: var(--color-text-secondary);">
              URL: {contentUrl}
            </p>
          </div>
        </div>
      {/if}
    </div>
  {/if}
</div>

<style>
  .book-reader-container {
    border: 1px solid var(--color-border);
    border-radius: 8px;
    overflow: hidden;
    background: var(--color-bg-primary);
  }

  .reader-header {
    padding: 12px 16px;
    background: var(--color-bg-secondary);
    border-bottom: 1px solid var(--color-border);
  }

  .reader-content {
    position: relative;
  }

  .fliphtml5-container {
    width: 100%;
    height: 100%;
  }

  .reader-loading,
  .reader-empty,
  .reader-error {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 2rem;
  }

  .reader-type-badge {
    display: flex;
    align-items: center;
  }

  .badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .badge-pdf {
    background: #ef4444;
    color: white;
  }

  .badge-fliphtml5 {
    background: #3b82f6;
    color: white;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .reader-header {
      padding: 8px 12px;
    }
    
    .reader-header h3 {
      font-size: 1rem;
    }
    
    .reader-header p {
      font-size: 0.75rem;
    }
  }
</style>
