#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to clean up orphaned digital product links
 * Usage: node scripts/cleanup-orphaned-links.js [--dry-run]
 */

require('dotenv').config()
const { createMedusaContainer } = require('@medusajs/framework')
const { ContainerRegistrationKeys } = require('@medusajs/framework/utils')

async function cleanupOrphanedLinks(dryRun = true) {
  try {
   
    const container = createMedusaContainer()
    const query = container.resolve(ContainerRegistrationKeys.QUERY)
    
    // Get all digital products
    const { data: digitalProducts } = await query.graph({
      entity: "digital_product",
      fields: ["id", "name", "product_variant_id", "created_at"]
    })
    
    if (!digitalProducts || digitalProducts.length === 0) {
      console.log('✅ No digital products found - nothing to clean up')
      return
    }
    
   
    // Check each digital product for valid variant link
    const orphanedProducts = []
    const duplicateVariants = new Map()
    
    for (const dp of digitalProducts) {
      try {
        // Check if the variant exists
        const { data: variants } = await query.graph({
          entity: "product_variant",
          fields: ["id", "title"],
          filters: { id: dp.product_variant_id }
        })
        
        if (!variants || variants.length === 0) {
          console.log(`❌ ORPHANED: "${dp.name}" → Missing variant ${dp.product_variant_id}`)
          orphanedProducts.push(dp)
        } else {
          // Check for duplicates
          if (duplicateVariants.has(dp.product_variant_id)) {
            const existing = duplicateVariants.get(dp.product_variant_id)
            console.log(`⚠️ DUPLICATE: Multiple digital products for variant ${dp.product_variant_id}:`)
            console.log(`   - "${existing.name}" (${existing.id}) - ${existing.created_at}`)
            console.log(`   - "${dp.name}" (${dp.id}) - ${dp.created_at}`)
            
            // Keep the newer one, mark older for deletion
            const existingDate = new Date(existing.created_at)
            const currentDate = new Date(dp.created_at)
            
            if (currentDate > existingDate) {
              orphanedProducts.push(existing)
              duplicateVariants.set(dp.product_variant_id, dp)
              console.log(`   → Will keep newer: "${dp.name}"`)
            } else {
              orphanedProducts.push(dp)
              console.log(`   → Will keep existing: "${existing.name}"`)
            }
          } else {
            duplicateVariants.set(dp.product_variant_id, dp)
            console.log(`✅ VALID: "${dp.name}" → ${variants[0].title}`)
          }
        }
      } catch (error) {
        console.log(`❌ ERROR checking "${dp.name}":`, error.message)
        orphanedProducts.push(dp)
      }
    }
    
    // Summary
    console.log(`\n📊 SUMMARY:`)
    console.log(`   Valid digital products: ${digitalProducts.length - orphanedProducts.length}`)
    console.log(`   Orphaned/duplicate products: ${orphanedProducts.length}`)
    
    if (orphanedProducts.length === 0) {
      console.log('✅ No cleanup needed!')
      return
    }
    
    // Show what will be deleted
    console.log(`\n🗑️ PRODUCTS TO DELETE:`)
    orphanedProducts.forEach(dp => {
      console.log(`   - "${dp.name}" (${dp.id})`)
    })
    
    if (dryRun) {
      console.log('\n💡 This was a dry run. To actually delete these products, run:')
      console.log('   node scripts/cleanup-orphaned-links.js --live')
      return
    }
    
    // Actually delete the orphaned products
    console.log('\n🗑️ Deleting orphaned products...')
    const digitalProductService = container.resolve('digitalProductModuleService')
    
    for (const dp of orphanedProducts) {
      try {
        await digitalProductService.deleteDigitalProducts([dp.id])
        console.log(`✅ Deleted: "${dp.name}"`)
      } catch (error) {
        console.log(`❌ Failed to delete "${dp.name}":`, error.message)
      }
    }
    
    console.log('\n🎉 Cleanup completed!')
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error)
  }
}

// Check command line arguments
const args = process.argv.slice(2)
const dryRun = !args.includes('--live')

cleanupOrphanedLinks(dryRun)
