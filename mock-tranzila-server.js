const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');

const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());
app.use(express.static('public'));

// Mock Tranzila payment page
app.get('/cgi-bin/tranzila71u.cgi', (req, res) => {
  const {
    supplier,
    sum,
    currency,
    myid,
    success_url,
    error_url,
    notify_url,
    contact,
    email,
    phone,
    iframe
  } = req.query;

  console.log('🔄 Mock Tranzila Payment Request:', {
    supplier,
    sum,
    currency,
    myid,
    contact,
    email,
    iframe: iframe === '1' ? 'Yes' : 'No'
  });

  // If iframe mode, return a simple payment form
  if (iframe === '1') {
    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Mock Tranzila Payment</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); margin: 0; min-height: 100vh; }
          .payment-form { background: white; padding: 30px; border-radius: 12px; max-width: 420px; margin: 0 auto; box-shadow: 0 8px 32px rgba(0,0,0,0.1); }
          .form-group { margin-bottom: 20px; }
          label { display: block; margin-bottom: 8px; font-weight: 600; color: #333; font-size: 14px; }
          input { width: 100%; padding: 14px; border: 2px solid #e1e5e9; border-radius: 8px; box-sizing: border-box; font-size: 16px; transition: border-color 0.3s ease; }
          input:focus { outline: none; border-color: #007cba; box-shadow: 0 0 0 3px rgba(0,124,186,0.1); }
          button { width: 100%; padding: 16px; background: linear-gradient(135deg, #007cba 0%, #005a87 100%); color: white; border: none; border-radius: 8px; font-size: 18px; font-weight: 600; cursor: pointer; transition: transform 0.2s ease; }
          button:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,124,186,0.3); }
          button:disabled { background: #ccc; cursor: not-allowed; transform: none; }
          .amount { font-size: 24px; font-weight: bold; color: #007cba; text-align: center; margin-bottom: 25px; padding: 15px; background: #f8f9fa; border-radius: 8px; }
          .test-cards { background: linear-gradient(135deg, #e8f4f8 0%, #f0f8ff 100%); padding: 20px; border-radius: 8px; margin-bottom: 25px; font-size: 13px; border-left: 4px solid #007cba; }
          .header { text-align: center; margin-bottom: 25px; }
          .logo { font-size: 28px; margin-bottom: 10px; }
          .processing { text-align: center; padding: 20px; }
          .spinner { width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #007cba; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 15px; }
          @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        </style>
      </head>
      <body>
        <div class="payment-form">
          <div class="header">
            <div class="logo">🏦</div>
            <h2 style="margin: 0; color: #333;">Secure Payment</h2>
          </div>
          <div class="amount">₪${sum} ${currency}</div>
          <div class="test-cards">
            <strong>🧪 Test Cards for Development:</strong><br>
            <strong>Success:</strong> ****************<br>
            <strong>Failure:</strong> ****************<br>
            <strong>Expiry:</strong> Any future date (MM/YY)<br>
            <strong>CVV:</strong> Any 3 digits
          </div>
          <form id="paymentForm">
            <div class="form-group">
              <label>💳 Card Number</label>
              <input type="text" id="cardNumber" placeholder="1234 5678 9012 3456" maxlength="19" required autocomplete="cc-number">
            </div>
            <div style="display: flex; gap: 15px;">
              <div class="form-group" style="flex: 1;">
                <label>📅 Expiry Date</label>
                <input type="text" id="expiry" placeholder="MM/YY" maxlength="5" required autocomplete="cc-exp">
              </div>
              <div class="form-group" style="flex: 1;">
                <label>🔒 CVV</label>
                <input type="text" id="cvv" placeholder="123" maxlength="4" required autocomplete="cc-csc">
              </div>
            </div>
            <div class="form-group">
              <label>👤 Cardholder Name</label>
              <input type="text" id="cardName" placeholder="John Doe" required autocomplete="cc-name">
            </div>
            <button type="submit" id="payButton">
              <span id="buttonText">Pay ₪${sum} ${currency}</span>
              <div id="buttonSpinner" class="spinner" style="display: none; width: 20px; height: 20px; margin: 0 auto;"></div>
            </button>
          </form>
        </div>

        <script>
          // Format card number with spaces
          document.getElementById('cardNumber').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            if (formattedValue !== e.target.value) {
              e.target.value = formattedValue;
            }
          });

          // Only allow numbers and slash for expiry
          document.getElementById('expiry').addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/[^0-9\/]/g, '');
          });

          // Only allow numbers for CVV
          document.getElementById('cvv').addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
          });

          document.getElementById('paymentForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const cardNumber = document.getElementById('cardNumber').value.replace(/\s/g, '');
            const expiry = document.getElementById('expiry').value;
            const cvv = document.getElementById('cvv').value;
            const cardName = document.getElementById('cardName').value;

            // Validate form
            if (!cardNumber || !expiry || !cvv || !cardName) {
              alert('Please fill in all fields');
              return;
            }

            const isSuccess = cardNumber === '****************';

            // Show processing state
            const button = document.getElementById('payButton');
            const buttonText = document.getElementById('buttonText');
            const buttonSpinner = document.getElementById('buttonSpinner');

            buttonText.style.display = 'none';
            buttonSpinner.style.display = 'block';
            button.disabled = true;

            setTimeout(() => {
              const result = {
                success: isSuccess,
                transactionId: '${myid}',
                responseCode: isSuccess ? '000' : '001',
                amount: '${sum}',
                currency: '${currency}',
                confirmationCode: isSuccess ? 'MOCK123456' : '',
                responseMessage: isSuccess ? 'Transaction approved' : 'Card declined',
                cardType: 'Visa',
                maskedCardNumber: '**** **** **** ' + cardNumber.slice(-4)
              };

              console.log('Payment result:', result);

              // Send result to parent window (for iframe mode)
              if (window.parent !== window) {
                window.parent.postMessage({
                  type: isSuccess ? 'tranzila_payment_complete' : 'tranzila_payment_error',
                  result: result
                }, '*');
              }

              // Also redirect for non-iframe mode
              const redirectUrl = isSuccess ? '${success_url}' : '${error_url}';
              if (redirectUrl && redirectUrl !== 'undefined') {
                const params = new URLSearchParams({
                  myid: '${myid}',
                  Response: result.responseCode,
                  sum: '${sum}',
                  currency: '${currency}',
                  ConfirmationCode: result.confirmationCode,
                  Responsemessage: result.responseMessage
                });
                window.location.href = redirectUrl + '?' + params.toString();
              }
            }, 2500);
          });
        </script>
      </body>
      </html>
    `);
  } else {
    // Redirect mode - show full page
    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Mock Tranzila Payment</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
          .payment-form { background: white; padding: 40px; border-radius: 8px; max-width: 500px; margin: 50px auto; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
          .form-group { margin-bottom: 20px; }
          label { display: block; margin-bottom: 8px; font-weight: bold; }
          input { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; font-size: 16px; }
          button { width: 100%; padding: 15px; background: #007cba; color: white; border: none; border-radius: 4px; font-size: 18px; cursor: pointer; }
          button:hover { background: #005a87; }
          .amount { font-size: 24px; font-weight: bold; color: #007cba; text-align: center; margin-bottom: 30px; }
          .test-cards { background: #e8f4f8; padding: 20px; border-radius: 4px; margin-bottom: 30px; }
          .header { text-align: center; margin-bottom: 30px; }
        </style>
      </head>
      <body>
        <div class="payment-form">
          <div class="header">
            <h1>🏦 Mock Tranzila Payment Gateway</h1>
            <div class="amount">Amount: ${sum} ${currency}</div>
          </div>
          
          <div class="test-cards">
            <h3>Test Card Numbers:</h3>
            <p><strong>Success:</strong> ****************</p>
            <p><strong>Failure:</strong> ****************</p>
            <p><strong>Expiry:</strong> Any future date (e.g., 12/25)</p>
            <p><strong>CVV:</strong> Any 3 digits (e.g., 123)</p>
          </div>

          <form id="paymentForm">
            <div class="form-group">
              <label>Card Number:</label>
              <input type="text" id="cardNumber" placeholder="Enter test card number" maxlength="16" required>
            </div>
            <div class="form-group">
              <label>Expiry Date (MM/YY):</label>
              <input type="text" id="expiry" placeholder="12/25" maxlength="5" required>
            </div>
            <div class="form-group">
              <label>CVV:</label>
              <input type="text" id="cvv" placeholder="123" maxlength="3" required>
            </div>
            <div class="form-group">
              <label>Cardholder Name:</label>
              <input type="text" id="cardName" placeholder="John Doe" required>
            </div>
            <button type="submit">Complete Payment</button>
          </form>
        </div>

        <script>
          document.getElementById('paymentForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const cardNumber = document.getElementById('cardNumber').value;
            const isSuccess = cardNumber === '****************';
            
            // Simulate processing delay
            const button = document.querySelector('button');
            button.textContent = 'Processing Payment...';
            button.disabled = true;
            
            setTimeout(() => {
              // First, send webhook notification
              fetch('/webhook-notify', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  myid: '${myid}',
                  Response: isSuccess ? '000' : '001',
                  sum: '${sum}',
                  currency: '${currency}',
                  ConfirmationCode: isSuccess ? 'MOCK123456' : '',
                  Responsemessage: isSuccess ? 'Success' : 'Card declined',
                  cardtype: 'Visa',
                  cardnum: '****' + cardNumber.slice(-4),
                  expmonth: '12',
                  expyear: '25',
                  notify_url: '${notify_url}'
                })
              }).then(() => {
                // Then redirect user
                const redirectUrl = isSuccess ? '${success_url}' : '${error_url}';
                const params = new URLSearchParams({
                  myid: '${myid}',
                  Response: isSuccess ? '000' : '001',
                  sum: '${sum}',
                  currency: '${currency}',
                  ConfirmationCode: isSuccess ? 'MOCK123456' : '',
                  Responsemessage: isSuccess ? 'Success' : 'Card declined'
                });
                
                window.location.href = redirectUrl + '?' + params.toString();
              });
            }, 3000);
          });
        </script>
      </body>
      </html>
    `);
  }
});

// Webhook notification endpoint
app.post('/webhook-notify', async (req, res) => {
  const { notify_url, ...webhookData } = req.body;
  
  console.log('🔔 Sending webhook notification to:', notify_url);
  console.log('📦 Webhook data:', webhookData);
  
  if (notify_url && notify_url !== 'undefined') {
    try {
      // Send webhook to the actual backend
      const fetch = require('node-fetch');
      const response = await fetch(notify_url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(webhookData)
      });
      
      console.log('✅ Webhook sent, status:', response.status);
    } catch (error) {
      console.error('❌ Webhook failed:', error.message);
    }
  }
  
  res.json({ status: 'ok' });
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'Mock Tranzila Server Running', port: PORT });
});

app.listen(PORT, () => {
  console.log(`🚀 Mock Tranzila Server running on http://localhost:${PORT}`);
  console.log(`📋 Payment endpoint: http://localhost:${PORT}/cgi-bin/tranzila71u.cgi`);
  console.log(`🔔 Webhook endpoint: http://localhost:${PORT}/webhook-notify`);
  console.log(`\n🧪 Test Cards:`);
  console.log(`   Success: ****************`);
  console.log(`   Failure: ****************`);
  console.log(`   Expiry: Any future date`);
  console.log(`   CVV: Any 3 digits\n`);
});
