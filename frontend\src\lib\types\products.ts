export type ProductType = 'chapter' | 'subscription' | 'bundle'
export type AccessType = 'permanent' | 'temporary' | 'subscription'
export type SubscriptionDuration = '6m' | '12m'

export interface Product {
  id: string
  variant_id?: string // For cart operations
  type: ProductType
  title: string
  subtitle?: string
  description: string
  short_description: string
  preview_content: string
  full_content?: string
  price: number
  formatted_price?: string // Formatted price with currency symbol
  original_price?: number // For discounted items
  currency: string
  access_type: AccessType
  duration?: SubscriptionDuration
  tags: string[]
  topics: string[]
  language: string
  reading_time_minutes: number
  order_in_book?: number
  is_published: boolean
  is_featured: boolean
  created_at: string
  updated_at: string

  // MedusaJS specific fields
  handle?: string
  collection_id?: string
  metadata?: Record<string, any>

  // Media
  cover_image?: string
  video_url?: string
  audio_url?: string
  
  // Relations
  related_products?: string[] // Product IDs
  bundle_items?: string[] // For bundles - included product IDs
  prerequisites?: string[] // Required products
  
  // Stats
  stats: {
    views: number
    purchases: number
  }
  
  // SEO
  seo: {
    meta_title: string
    meta_description: string
    keywords: string[]
    slug: string
  }
}

export interface CartItem {
  product_id: string
  product: Product
  quantity: number
  added_at: string
  // Backend cart item properties
  id?: string           // Line item ID from MedusaJS
  variant_id?: string   // Product variant ID
  unit_price?: number   // Price per unit from backend
}

export interface Cart {
  items: CartItem[]
  total_amount: number
  currency: string
  discount_code?: string
  discount_amount?: number
}

export interface ProductFilter {
  topics?: string[]
  product_types?: ProductType[]
  access_types?: AccessType[]
  price_range?: {
    min: number
    max: number
  }
  search_query?: string
  is_featured?: boolean
  language?: string
}

export interface ProductSort {
  field: 'title' | 'price' | 'created_at' | 'popularity'
  direction: 'asc' | 'desc'
}

export interface Recommendation {
  product: Product
  reason: 'similar_topic' | 'same_difficulty' | 'frequently_bought_together' | 'trending'
  score: number
}

export interface UserAccess {
  product_id: string
  access_type: AccessType
  granted_at: string
  expires_at?: string
  is_active: boolean
}
