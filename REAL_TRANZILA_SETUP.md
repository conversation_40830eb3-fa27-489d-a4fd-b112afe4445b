# Real Tranzila Integration Setup

## 1. Get Your Tranzila Credentials

From your Tranzila merchant account, you'll need:

### Required Credentials:
- **Terminal Name**: Your assigned terminal name (primary identifier)
- **API Key**: For API access (if using advanced features)

### Where to Find Them:
1. Log into your Tranzila merchant panel
2. Go to **Settings** → **Terminal Settings**
3. Copy your **Terminal Name** (this is your main identifier)
4. If using API features, get your **API Key** from API settings

## 2. Update Backend Environment Variables

Replace the placeholder values in `backend/.env`:

```bash
# Tranzila Configuration (replace with your real credentials)
TRANZILA_TERMINAL_NAME=your_actual_terminal_name
TRANZILA_API_KEY=your_api_key_if_needed
TRANZILA_CURRENCY=ILS
TRANZILA_LANGUAGE=he
TRANZILA_TEST_MODE=true
TRANZILA_IFRAME_MODE=true

# URLs for callbacks
MEDUSA_BACKEND_URL=http://localhost:9000
FRONTEND_URL=http://localhost:5173
```

## 3. Configure Tranzila Merchant Panel

In your Tranzila merchant panel, configure these URLs:

### Success URL:
```
http://localhost:5173/checkout/tranzila/success
```

### Error URL:
```
http://localhost:5173/checkout/tranzila/error
```

### Notify URL (Webhook):
```
http://localhost:9000/webhooks/tranzila
```

**Note**: For production, replace `localhost` with your actual domain.

## 4. Test Cards

Tranzila provides these test cards for sandbox testing:

### Success Cards:
- **Visa**: ****************
- **MasterCard**: ****************
- **Israeli**: 5892105892105892

### Decline Cards:
- **Insufficient Funds**: ****************
- **Invalid Card**: ****************

### Test Details:
- **Expiry**: Any future date (MM/YY)
- **CVV**: Any 3-4 digits
- **Name**: Any name

## 5. Integration Flow

### Iframe Mode (Recommended):
1. Customer proceeds to checkout
2. Frontend generates payment URL via backend
3. Tranzila iframe loads with payment form
4. Customer enters card details
5. Tranzila processes payment
6. Result sent via postMessage to parent window
7. Frontend completes order

### Redirect Mode:
1. Customer proceeds to checkout
2. Frontend generates payment URL via backend
3. Customer redirected to Tranzila payment page
4. Customer enters card details
5. Tranzila processes payment and redirects back
6. Frontend handles success/error pages

## 6. Testing Steps

1. **Start your services**:
   ```bash
   # Backend
   cd backend && yarn dev
   
   # Frontend
   cd frontend && yarn dev
   ```

2. **Test the flow**:
   - Add items to cart
   - Proceed to checkout
   - Select Tranzila payment
   - Use test card: `****************`
   - Verify payment completes successfully

3. **Check logs**:
   - Backend: Payment URL generation
   - Frontend: Iframe loading and message handling
   - Browser Network: API calls to Tranzila

## 7. Production Deployment

### Environment Variables:
```bash
TRANZILA_TEST_MODE=false
MEDUSA_BACKEND_URL=https://your-api-domain.com
FRONTEND_URL=https://your-frontend-domain.com
```

### Tranzila Panel URLs:
- Success: `https://your-frontend-domain.com/checkout/tranzila/success`
- Error: `https://your-frontend-domain.com/checkout/tranzila/error`
- Notify: `https://your-api-domain.com/webhooks/tranzila`

## 8. Security Considerations

### SSL Required:
- All URLs must use HTTPS in production
- Tranzila requires SSL for iframe integration

### Origin Validation:
- Frontend validates messages from Tranzila domains only
- Webhook endpoint validates Tranzila signatures

### PCI Compliance:
- Card data never touches your servers
- Tranzila handles all sensitive information

## 9. Troubleshooting

### Common Issues:

**Payment URL not generated**:
- Check Tranzila credentials in .env
- Verify PAYMENT_PROVIDER=tranzila
- Check backend logs for errors

**Iframe not loading**:
- Verify VITE_TRANZILA_IFRAME_MODE=true in frontend/.env
- Check browser console for CORS errors
- Ensure Tranzila URLs are accessible

**Payment not completing**:
- Check browser console for postMessage errors
- Verify webhook URL is accessible
- Check Tranzila merchant panel for transaction logs

**Amount showing as NaN**:
- Check cart.total is properly calculated
- Verify currency conversion in backend
- Check frontend cart state

## 10. Support

### Tranzila Documentation:
- [Iframe Integration](https://docs.tranzila.com/docs/payments-billing/795m2yi7q4nmq-iframe-integration)
- [API Reference](https://docs.tranzila.com/docs/payments-billing/api-reference)

### Tranzila Support:
- Technical support through merchant panel
- Email: <EMAIL>
- Phone: Check your merchant account for support number

This setup provides a complete, production-ready Tranzila integration with proper security and error handling.
