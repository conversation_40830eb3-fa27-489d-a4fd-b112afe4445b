<script lang="ts">
  import { onMount } from 'svelte'
  import { goto } from '$app/navigation'
  import { _ } from '$lib/i18n'
  import { apiClient } from '$lib/api/client'
  import StripePayment from './StripeSimple.svelte'
  import TranzilaPayment from './TranzilaPayment.svelte'

  export let cart: any
  export let selectedProvider: string | null = null
  export let isLoading: boolean = false
  export let onSubmit: (provider: string) => void
  export let onBack: () => void

  let paymentProviders: any[] = []
  let selectedPaymentProvider = selectedProvider
  let loadingProviders = false
  let providersError = ''
  let paymentSessionsInitialized = false

  // Available payment providers - will be populated from backend
  let availableProviders: any[] = []

  // Fallback providers for development
  const fallbackProviders = [
    {
      id: 'stripe',
      name: 'Credit Card',
      description: 'Pay securely with your credit or debit card',
      icon: '💳',
      provider_id: 'pp_stripe_stripe'
    },
    {
      id: 'tranzila',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      description: 'Pay with Israeli credit cards via Tranzila',
      icon: '🏦',
      provider_id: 'pp_tranzila_tranzila'
    }
  ]

  onMount(() => {
    initializePaymentSessions()
  })

  async function initializePaymentSessions() {
    try {
      loadingProviders = true
      providersError = ''

      if (!cart?.id) {
        throw new Error('No cart found')
      }

      // Step 1: Get available payment providers for the region
      console.log('🔄 Getting payment providers for region:', cart)
      const providersResponse = await apiClient.getPaymentProviders(cart.region_id)
      if (providersResponse.data?.payment_providers) {
        paymentProviders = providersResponse.data.payment_providers
        console.log('✅ Payment providers loaded:', paymentProviders.length)

        // Map backend providers to frontend display providers
        availableProviders = paymentProviders.map(provider => {
          const fallback = fallbackProviders.find(fp =>
            provider.id.includes(fp.id) || fp.provider_id.includes(provider.id)
          )
          return fallback || {
            id: provider.id,
            name: provider.id,
            description: `Pay with ${provider.id}`,
            icon: '💳',
            provider_id: provider.id
          }
        })
        console.log('✅ Available providers mapped:', availableProviders)
      }

      // Step 2: Initialize payment sessions for available providers

      // Initialize with the first available provider
      const firstProvider = availableProviders[0]
      if (!firstProvider) {
        throw new Error('No payment providers available')
      }

      console.log('🔄 Initiating payment session with provider:', firstProvider.provider_id)
      const response = await apiClient.initiatePaymentSession(cart, {
        provider_id: firstProvider.provider_id
      })

      console.log('🔍 Payment session response:', response.data)

      if (!response.data?.payment_collection) {
        console.error('❌ No payment collection in response:', response)
        throw new Error('Payment session initialization failed - no payment collection returned')
      }

      if (response.data?.payment_collection) {
        paymentSessionsInitialized = true
        console.log('✅ Payment session initialized')
        console.log('🔍 Payment collection response:', response.data.payment_collection)

        // Update cart with payment collection
        cart.payment_collection = response.data.payment_collection

        // Pre-select the first available provider
        if (!selectedPaymentProvider && availableProviders.length > 0) {
          selectedPaymentProvider = availableProviders[0].id
        }
      } else {
        throw new Error('Failed to initialize payment session')
      }
    } catch (error) {
      console.error('❌ Failed to initialize payment session:', error)
      providersError = $_('checkout.failed_to_initialize_payment')
    } finally {
      loadingProviders = false
    }
  }

  function selectProvider(providerId: string) {
    selectedPaymentProvider = providerId
  }

  async function handleProviderSelection() {
    if (!selectedPaymentProvider) {
      return
    }

    // Find the provider configuration
    const provider = availableProviders.find(p => p.id === selectedPaymentProvider)
    if (!provider) {
      return
    }

    try {
      const paymentCollectionId = cart.payment_collection?.id
      if (!paymentCollectionId) {
        throw new Error('No payment collection found')
      }

      // Set the payment provider
      console.log('🔄 Setting payment provider:', provider.provider_id)
      await apiClient.setPaymentProvider(paymentCollectionId, provider.provider_id)
      console.log('✅ Payment provider set')
      onSubmit(selectedPaymentProvider)
    } catch (error) {
      console.error('❌ Failed to set payment provider:', error)
      providersError = $_('checkout.failed_to_set_payment_provider')
    }
  }

  async function handlePaymentComplete(result: { success: boolean, order?: any, error?: string }) {
    if (result.success && result.order) {
      // Payment successful, redirect to order confirmation
      goto(`/orders/${result.order.id}/confirmation`)
    } else {
      // Payment failed
      providersError = result.error || $_('checkout.payment_failed')
    }
  }
</script>

<div class="card-classic p-6">
  <div class="mb-6">
    <h2 class="font-book-title text-xl font-bold mb-2" style="color: var(--color-text-primary);">
      💳 {$_('checkout.step_payment')}
    </h2>
    <p class="font-book-text" style="color: var(--color-text-secondary);">
      {$_('checkout.payment_description')}
    </p>
  </div>

  {#if loadingProviders}
    <div class="text-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 mx-auto mb-4" style="border-color: var(--color-primary);"></div>
      <p class="font-book-text" style="color: var(--color-text-secondary);">
        {$_('checkout.initializing_payment')}
      </p>
    </div>
  {:else if providersError}
    <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
      <p class="text-red-800 font-book-text">{providersError}</p>
      <button on:click={initializePaymentSessions} class="mt-2 btn-classic-small">
        {$_('checkout.retry')}
      </button>
    </div>
  {:else if !paymentSessionsInitialized}
    <div class="text-center py-8">
      <div class="text-4xl mb-4">⚠️</div>
      <h3 class="font-book-title text-lg font-semibold mb-2" style="color: var(--color-text-primary);">
        {$_('checkout.payment_not_ready')}
      </h3>
      <p class="font-book-text text-sm" style="color: var(--color-text-secondary);">
        {$_('checkout.payment_not_ready_description')}
      </p>
    </div>
  {:else}
    <!-- Payment Provider Selection -->
    {#if !selectedPaymentProvider}
      <div class="space-y-4 mb-6">
        <h3 class="font-book-title text-lg font-semibold" style="color: var(--color-text-primary);">
          {$_('checkout.select_payment_method')}
        </h3>
        
        {#each availableProviders as provider (provider.id)}
          <label class="block cursor-pointer">
            <div class="border-2 rounded-lg p-4 transition-all duration-200 hover:border-blue-300
              {selectedPaymentProvider === provider.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:bg-gray-50'}"
            >
              <div class="flex items-center">
                <input
                  type="radio"
                  name="payment_provider"
                  value={provider.id}
                  checked={selectedPaymentProvider === provider.id}
                  on:change={() => selectProvider(provider.id)}
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <div class="ml-3 flex-1">
                  <div class="flex items-center">
                    <span class="text-2xl mr-3">{provider.icon}</span>
                    <div>
                      <h4 class="font-book-title text-sm font-semibold" style="color: var(--color-text-primary);">
                        {provider.name}
                      </h4>
                      <p class="font-book-text text-xs mt-1" style="color: var(--color-text-secondary);">
                        {provider.description}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </label>
        {/each}

        <div class="flex justify-between pt-4">
          <button
            type="button"
            on:click={onBack}
            disabled={isLoading}
            class="btn-classic-outline"
            class:opacity-50={isLoading}
          >
            ← {$_('checkout.back')}
          </button>

          <button
            type="button"
            on:click={handleProviderSelection}
            disabled={isLoading || !selectedPaymentProvider}
            class="btn-classic"
            class:opacity-50={isLoading || !selectedPaymentProvider}
          >
            {#if isLoading}
              <div class="flex items-center">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {$_('checkout.processing')}
              </div>
            {:else}
              {$_('checkout.continue')} →
            {/if}
          </button>
        </div>
      </div>
    {:else}
      <!-- Payment Form -->
      <div class="space-y-6">
        <div class="flex items-center justify-between">
          <h3 class="font-book-title text-lg font-semibold" style="color: var(--color-text-primary);">
            {$_('checkout.complete_payment')}
          </h3>
          <button
            type="button"
            on:click={() => selectedPaymentProvider = null}
            class="text-sm text-blue-600 hover:text-blue-800 font-book-text"
          >
            {$_('checkout.change_payment_method')}
          </button>
        </div>

        {#if selectedPaymentProvider === 'stripe'}
          <StripePayment
            {cart}
            onComplete={handlePaymentComplete}
            onBack={onBack}
          />
        {:else if selectedPaymentProvider === 'tranzila'}
          <TranzilaPayment
            {cart}
            onComplete={handlePaymentComplete}
            onBack={onBack}
          />
        {/if}
      </div>
    {/if}
  {/if}
</div>
