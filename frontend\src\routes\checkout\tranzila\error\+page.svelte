<script lang="ts">
  import { onMount } from 'svelte'
  import { goto } from '$app/navigation'
  import { page } from '$app/stores'
  import { _ } from '$lib/i18n'

  let transactionDetails: any = {}
  let errorMessage = ''

  onMount(() => {
    // Get transaction details from URL parameters
    const urlParams = $page.url.searchParams
    transactionDetails = {
      transactionId: urlParams.get('transaction_id'),
      responseCode: urlParams.get('response_code'),
      amount: urlParams.get('amount'),
      currency: urlParams.get('currency'),
      message: urlParams.get('message'),
      error: urlParams.get('error'),
    }

    console.log('❌ Tranzila payment error callback:', transactionDetails)

    // Set error message based on response code or generic error
    if (transactionDetails.message) {
      errorMessage = transactionDetails.message
    } else if (transactionDetails.error) {
      errorMessage = getErrorMessage(transactionDetails.error)
    } else if (transactionDetails.responseCode) {
      errorMessage = getTranzilaErrorMessage(transactionDetails.responseCode)
    } else {
      errorMessage = $_('checkout.payment_failed_generic')
    }

    // Clean up session storage
    sessionStorage.removeItem('tranzila_cart_id')
    sessionStorage.removeItem('tranzila_transaction_id')
  })

  function getErrorMessage(errorCode: string): string {
    const errorMessages: Record<string, string> = {
      'callback_processing_failed': $_('checkout.callback_processing_failed'),
      'invalid_transaction': $_('checkout.invalid_transaction'),
      'timeout': $_('checkout.payment_timeout'),
      'cancelled': $_('checkout.payment_cancelled'),
    }
    
    return errorMessages[errorCode] || $_('checkout.payment_failed_generic')
  }

  function getTranzilaErrorMessage(responseCode: string): string {
    // Common Tranzila response codes and their meanings
    const tranzilaErrors: Record<string, string> = {
      '001': $_('checkout.tranzila_error_001'), // Invalid credit card number
      '002': $_('checkout.tranzila_error_002'), // Invalid expiry date
      '003': $_('checkout.tranzila_error_003'), // Invalid CVV
      '004': $_('checkout.tranzila_error_004'), // Insufficient funds
      '005': $_('checkout.tranzila_error_005'), // Card declined
      '006': $_('checkout.tranzila_error_006'), // Invalid amount
      '007': $_('checkout.tranzila_error_007'), // Invalid currency
      '008': $_('checkout.tranzila_error_008'), // Transaction not allowed
      '009': $_('checkout.tranzila_error_009'), // Card expired
      '010': $_('checkout.tranzila_error_010'), // Invalid merchant
      '999': $_('checkout.tranzila_error_999'), // System error
    }
    
    return tranzilaErrors[responseCode] || $_('checkout.tranzila_error_unknown')
  }

  function handleRetryPayment() {
    // Return to checkout to try again
    goto('/checkout')
  }

  function handleContactSupport() {
    // You can implement contact support functionality here
    // For now, just redirect to a contact page or show contact info
    goto('/contact')
  }
</script>

<svelte:head>
  <title>{$_('checkout.payment_failed')} - Hebrew Book Store</title>
</svelte:head>

<div class="error-page">
  <div class="container">
    <div class="error-content">
      <div class="error-icon">❌</div>
      <h1>{$_('checkout.payment_failed')}</h1>
      <p class="error-message">{errorMessage}</p>
      
      {#if transactionDetails.transactionId || transactionDetails.responseCode}
        <div class="error-details">
          <h2>{$_('checkout.transaction_details')}</h2>
          {#if transactionDetails.transactionId}
            <div class="detail-row">
              <span class="label">{$_('checkout.transaction_id')}:</span>
              <span class="value">{transactionDetails.transactionId}</span>
            </div>
          {/if}
          {#if transactionDetails.responseCode}
            <div class="detail-row">
              <span class="label">{$_('checkout.error_code')}:</span>
              <span class="value">{transactionDetails.responseCode}</span>
            </div>
          {/if}
          {#if transactionDetails.amount && transactionDetails.currency}
            <div class="detail-row">
              <span class="label">{$_('checkout.amount')}:</span>
              <span class="value">{transactionDetails.amount} {transactionDetails.currency}</span>
            </div>
          {/if}
        </div>
      {/if}

      <div class="help-section">
        <h3>{$_('checkout.what_happened')}</h3>
        <ul class="help-list">
          <li>{$_('checkout.payment_not_processed')}</li>
          <li>{$_('checkout.no_charge_made')}</li>
          <li>{$_('checkout.cart_still_available')}</li>
        </ul>
      </div>

      <div class="suggestions">
        <h3>{$_('checkout.what_to_do')}</h3>
        <ul class="suggestion-list">
          <li>{$_('checkout.check_card_details')}</li>
          <li>{$_('checkout.ensure_sufficient_funds')}</li>
          <li>{$_('checkout.try_different_card')}</li>
          <li>{$_('checkout.contact_bank_if_needed')}</li>
        </ul>
      </div>

      <div class="actions">
        <button on:click={handleRetryPayment} class="button primary">
          {$_('checkout.try_again')}
        </button>
        <button on:click={() => goto('/')} class="button secondary">
          {$_('common.home')}
        </button>
        <button on:click={handleContactSupport} class="button tertiary">
          {$_('checkout.contact_support')}
        </button>
      </div>
    </div>
  </div>
</div>

<style>
  .error-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-color);
    padding: 2rem;
  }

  .container {
    max-width: 700px;
    width: 100%;
  }

  .error-content {
    background: white;
    padding: 3rem 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
  }

  .error-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
  }

  h1 {
    color: var(--error-color);
    margin-bottom: 1rem;
    font-size: 2rem;
  }

  .error-message {
    color: var(--text-color);
    margin-bottom: 2rem;
    font-size: 1.1rem;
    font-weight: 500;
  }

  .error-details {
    background: var(--error-bg);
    border: 1px solid var(--error-border);
    padding: 1.5rem;
    border-radius: 8px;
    margin: 2rem 0;
    text-align: left;
  }

  .error-details h2 {
    color: var(--error-color);
    margin-bottom: 1rem;
    font-size: 1.3rem;
    text-align: center;
  }

  .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--error-border);
  }

  .detail-row:last-child {
    border-bottom: none;
  }

  .label {
    font-weight: 600;
    color: var(--text-color);
  }

  .value {
    color: var(--text-secondary);
    font-family: monospace;
  }

  .help-section, .suggestions {
    background: var(--bg-light);
    padding: 1.5rem;
    border-radius: 8px;
    margin: 2rem 0;
    text-align: left;
  }

  .help-section h3, .suggestions h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.2rem;
    text-align: center;
  }

  .help-list, .suggestion-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .help-list li, .suggestion-list li {
    padding: 0.5rem 0;
    position: relative;
    padding-left: 1.5rem;
    color: var(--text-color);
  }

  .help-list li::before {
    content: "ℹ️";
    position: absolute;
    left: 0;
  }

  .suggestion-list li::before {
    content: "💡";
    position: absolute;
    left: 0;
  }

  .actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 2rem;
  }

  .button {
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
    border: none;
  }

  .button.primary {
    background: var(--primary-color);
    color: white;
  }

  .button.primary:hover {
    background: var(--primary-hover);
  }

  .button.secondary {
    background: var(--secondary-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
  }

  .button.secondary:hover {
    background: var(--hover-bg);
  }

  .button.tertiary {
    background: var(--warning-color);
    color: white;
  }

  .button.tertiary:hover {
    background: var(--warning-hover);
  }

  @media (max-width: 768px) {
    .error-page {
      padding: 1rem;
    }

    .error-content {
      padding: 2rem 1rem;
    }

    .actions {
      flex-direction: column;
    }

    .button {
      width: 100%;
    }

    .detail-row {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.25rem;
    }
  }
</style>
