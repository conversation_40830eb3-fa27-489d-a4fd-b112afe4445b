import { defineWidgetConfig } from "@medusajs/admin-sdk"
import { useState, useEffect } from "react"
import {
  AdminProduct,
  DetailWidgetProps,
} from "@medusajs/framework/types"
import {
  Container,
  Heading,
  Button,
  Label,
  Text,
  toast,
} from "@medusajs/ui"

type ProductImage = {
  id: string
  url: string
  metadata?: Record<string, any>
}

const ProductImageManagerWidget = ({ data }: DetailWidgetProps<AdminProduct>) => {
  const [product, setProduct] = useState<AdminProduct>(data)
  const [isUpdating, setIsUpdating] = useState(false)

  useEffect(() => {
    setProduct(data)
  }, [data])

  const handleSetThumbnail = async (imageUrl: string) => {
    if (!product?.id) {
      toast.error("Product ID not found")
      return
    }

    setIsUpdating(true)
    try {
      const currentThumbnail = product.thumbnail
      const currentImages = product.images || []

      // Prepare the updated images array
      let updatedImages = [...currentImages]

      // If there's a current thumbnail and it's not already in the images array, add it
      if (currentThumbnail && !currentImages.some(img => img.url === currentThumbnail)) {
        // Create a new image entry for the previous thumbnail
        const thumbnailAsImage = {
          url: currentThumbnail,
          metadata: {}
        }
        updatedImages.push(thumbnailAsImage)
      }

      // Use the standard MedusaJS v2 admin API pattern
      const response = await fetch(`/admin/products/${product.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          thumbnail: imageUrl,
          images: updatedImages.map(img => ({
            url: img.url,
            metadata: img.metadata || {}
          }))
        }),
      })

      if (!response.ok) {
        const errorData = await response.text()
        console.error('API Error:', errorData)
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      console.log('Update result:', result)

      // Update local state
      setProduct(prev => ({
        ...prev,
        thumbnail: imageUrl,
        images: updatedImages
      }))

      if (currentThumbnail && !currentImages.some(img => img.url === currentThumbnail)) {
        toast.success("✅ Thumbnail updated! Previous thumbnail preserved as additional image.")
      } else {
        toast.success("✅ Thumbnail updated successfully!")
      }

      // Refresh the page to show updated data
      setTimeout(() => {
        window.location.reload()
      }, 1500)

    } catch (error) {
      console.error('Error updating thumbnail:', error)
      toast.error(`❌ Failed to update thumbnail: ${error.message}`)
    } finally {
      setIsUpdating(false)
    }
  }

  const handleRemoveThumbnail = async () => {
    if (!product?.id) {
      toast.error("Product ID not found")
      return
    }

    setIsUpdating(true)
    try {
      const response = await fetch(`/admin/products/${product.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          thumbnail: null,
        }),
      })

      if (!response.ok) {
        const errorData = await response.text()
        console.error('API Error:', errorData)
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      console.log('Remove result:', result)

      // Update local state
      setProduct(prev => ({
        ...prev,
        thumbnail: null
      }))

      toast.success("✅ Thumbnail removed successfully!")

      // Refresh the page to show updated data
      setTimeout(() => {
        window.location.reload()
      }, 1500)

    } catch (error) {
      console.error('Error removing thumbnail:', error)
      toast.error(`❌ Failed to remove thumbnail: ${error.message}`)
    } finally {
      setIsUpdating(false)
    }
  }

  if (!product) {
    return (
      <Container className="p-6">
        <Text>Loading product data...</Text>
      </Container>
    )
  }

  const images = product.images || []
  const currentThumbnail = product.thumbnail

  return (
    <Container className="p-6">
      <div className="flex items-center justify-between mb-4">
        <Heading level="h2">🖼️ Product Images & Thumbnail</Heading>
      </div>

      <div className="space-y-6">
        {/* Current Thumbnail Section */}
        <div>
          <Label className="text-sm font-medium text-gray-900 mb-2 block">
            Current Thumbnail (Cover Image)
          </Label>
          {currentThumbnail ? (
            <div className="flex items-start gap-4 p-4 bg-green-50 border border-green-200 rounded-lg">
              <img
                src={currentThumbnail}
                alt="Current thumbnail"
                className="w-20 h-20 object-cover rounded-md border"
              />
              <div className="flex-1">
                <Text className="text-sm font-medium text-green-800 mb-1">
                  ✅ Active Thumbnail
                </Text>
                <Text className="text-xs text-green-600 break-all mb-2">
                  {currentThumbnail}
                </Text>
                <Text className="text-xs text-gray-500 mb-2">
                  💡 When you set a new thumbnail, this image will be preserved as an additional image
                </Text>
                <Button
                  variant="secondary"
                  size="small"
                  onClick={handleRemoveThumbnail}
                  disabled={isUpdating}
                  className="text-red-600 hover:text-red-700"
                >
                  {isUpdating ? "Removing..." : "🗑️ Remove Thumbnail"}
                </Button>
              </div>
            </div>
          ) : (
            <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
              <Text className="text-sm text-gray-600">
                📷 No thumbnail set. Select an image below to make it the thumbnail.
              </Text>
            </div>
          )}
        </div>

        {/* Available Images Section */}
        <div>
          <Label className="text-sm font-medium text-gray-900 mb-2 block">
            Available Images ({images.length})
          </Label>
          {images.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {images.map((image: ProductImage) => {
                const isCurrentThumbnail = image.url === currentThumbnail
                return (
                  <div
                    key={image.id}
                    className={`p-4 border rounded-lg ${
                      isCurrentThumbnail
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200 bg-white hover:border-blue-300'
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <img
                        src={image.url}
                        alt="Product image"
                        className="w-16 h-16 object-cover rounded-md border"
                      />
                      <div className="flex-1 min-w-0">
                        <Text className="text-xs text-gray-500 break-all mb-2">
                          {image.url}
                        </Text>
                        {isCurrentThumbnail ? (
                          <div className="flex items-center gap-1">
                            <span className="text-xs font-medium text-green-600">
                              ✅ Current Thumbnail
                            </span>
                          </div>
                        ) : (
                          <Button
                            variant="secondary"
                            size="small"
                            onClick={() => handleSetThumbnail(image.url)}
                            disabled={isUpdating}
                            className="text-blue-600 hover:text-blue-700"
                            title="Set as thumbnail (previous thumbnail will be preserved as image)"
                          >
                            {isUpdating ? "Setting..." : "📌 Set as Cover"}
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
              <Text className="text-sm text-gray-600 mb-2">
                📷 No images uploaded yet.
              </Text>
              <Text className="text-xs text-gray-500">
                Upload images in the "Media" section above, then return here to set a thumbnail.
              </Text>
            </div>
          )}
        </div>

        {/* Help Section */}
        <div className="pt-4 border-t bg-blue-50 p-3 rounded-lg">
          <Text className="text-xs text-blue-800">
            💡 <strong>How it works:</strong> The thumbnail image will be used as the book cover on your store's main page and catalog.
            When you set a new thumbnail, the previous thumbnail is automatically preserved as an additional image.
            Upload images using the "Media" section above, then use the "Make Thumbnail" button to set which image should be the cover.
          </Text>
        </div>
      </div>
    </Container>
  )
}

export const config = defineWidgetConfig({
  zone: "product.details.after",
})

export default ProductImageManagerWidget
