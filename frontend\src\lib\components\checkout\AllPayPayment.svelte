<script lang="ts">
  import { onMount } from 'svelte'
  import { _ } from '$lib/i18n'
  import { apiClient } from '$lib/api/client'

  export let cart: any
  export let onComplete: (result: any) => void
  export let onBack: () => void

  let isProcessing = false
  let paymentError = ''
  let paymentUrl = ''
  let transactionId = ''
  let useIframe = true
  let iframeLoaded = false

  // AllPay iframe mode from environment
  const allpayIframeMode = import.meta.env.VITE_ALLPAY_IFRAME_MODE === 'true'

  onMount(() => {
    useIframe = allpayIframeMode
    console.log('🔍 AllPay iframe mode:', allpayIframeMode, 'useIframe:', useIframe)
    console.log('🔍 Environment variable:', import.meta.env.VITE_ALLPAY_IFRAME_MODE)
    generatePaymentUrl()
  })

  async function generatePaymentUrl() {
    try {
      isProcessing = true
      paymentError = ''

      console.log('🔄 Initiating AllPay payment session for cart:', cart)
      console.log('💰 Cart total:', cart.total_amount, cart.currency_code)
      console.log('🔍 Payment collection:', cart.payment_collection)

      // Use proper Medusa payment workflow
      if (!cart.payment_collection?.id) {
        throw new Error('No payment collection found. Please refresh and try again.')
      }

      // Create payment session using Medusa's payment workflow
      const paymentSession = await apiClient.initiatePaymentSession(cart, {
        provider_id: 'pp_allpay_allpay'
      })

      console.log('🔍 AllPay payment session response:', paymentSession.data)

      if (!paymentSession.data?.payment_collection?.payment_sessions) {
        throw new Error('Failed to create AllPay payment session')
      }

      // Find AllPay payment session
      const allpaySession = paymentSession.data.payment_collection.payment_sessions.find(
        (session: any) => session.provider_id === 'pp_allpay_allpay'
      )

      if (!allpaySession) {
        throw new Error('AllPay payment session not found')
      }

      console.log('🔍 AllPay session data:', allpaySession)

      // Extract payment URL and transaction ID from session data
      if (allpaySession.data?.payment_url) {
        paymentUrl = allpaySession.data.payment_url
        transactionId = allpaySession.data.order_id || allpaySession.id
        
        console.log('✅ AllPay payment URL generated:', paymentUrl)
        console.log('🔍 Transaction ID:', transactionId)
      } else {
        throw new Error('No payment URL received from AllPay')
      }

    } catch (error) {
      console.error('❌ AllPay payment URL generation failed:', error)
      paymentError = error.message || $_('checkout.payment_initialization_failed')
    } finally {
      isProcessing = false
    }
  }

  function handleRedirectPayment() {
    if (!paymentUrl) {
      paymentError = $_('checkout.payment_url_not_available')
      return
    }

    console.log('🔄 Redirecting to AllPay payment page')
    
    // Store cart ID and transaction ID in sessionStorage for return handling
    sessionStorage.setItem('allpay_cart_id', cart.id)
    sessionStorage.setItem('allpay_transaction_id', transactionId)
    
    // Redirect to AllPay payment page
    window.location.href = paymentUrl
  }

  function handleIframeLoad() {
    iframeLoaded = true
    console.log('✅ AllPay iframe loaded')
  }

  function handleIframeError() {
    console.error('❌ AllPay iframe failed to load')
    paymentError = $_('checkout.iframe_load_failed')
    iframeLoaded = false
  }

  // Listen for iframe messages (AllPay payment completion)
  onMount(() => {
    const handleMessage = (event: MessageEvent) => {
      console.log('📨 Received iframe message:', event)
      
      // Verify origin for security
      if (!event.origin.includes('allpay.to')) {
        console.warn('⚠️ Ignoring message from unknown origin:', event.origin)
        return
      }

      const data = event.data
      
      if (data.type === 'ALLPAY_PAYMENT_SUCCESS') {
        console.log('✅ AllPay payment successful via iframe')
        handlePaymentSuccess(data)
      } else if (data.type === 'ALLPAY_PAYMENT_ERROR') {
        console.error('❌ AllPay payment failed via iframe:', data)
        handlePaymentError(data)
      }
    }

    window.addEventListener('message', handleMessage)
    
    return () => {
      window.removeEventListener('message', handleMessage)
    }
  })

  async function handlePaymentSuccess(data: any) {
    try {
      console.log('🔄 Processing AllPay payment success:', data)
      
      // Verify payment status with backend
      const statusResponse = await fetch('/api/store/allpay/payment-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          order_id: transactionId
        }),
      })

      if (!statusResponse.ok) {
        throw new Error('Failed to verify payment status')
      }

      const statusData = await statusResponse.json()
      
      if (statusData.status === 1) { // Payment successful
        console.log('✅ AllPay payment verified as successful')
        onComplete({
          success: true,
          payment_method: 'allpay',
          transaction_id: transactionId,
          amount: statusData.amount,
          currency: statusData.currency,
          card_mask: statusData.card_mask,
          card_brand: statusData.card_brand,
        })
      } else {
        throw new Error('Payment verification failed')
      }
    } catch (error) {
      console.error('❌ AllPay payment success handling failed:', error)
      paymentError = error.message || $_('checkout.payment_verification_failed')
    }
  }

  function handlePaymentError(data: any) {
    console.error('❌ AllPay payment error:', data)
    paymentError = data.message || $_('checkout.payment_failed')
  }
</script>

<div class="allpay-payment">
  {#if paymentError}
    <div class="error-message">
      <p>{paymentError}</p>
      <button type="button" on:click={generatePaymentUrl} class="retry-button">
        {$_('checkout.retry')}
      </button>
    </div>
  {:else if isProcessing}
    <div class="loading-state">
      <div class="spinner"></div>
      <p>{$_('checkout.preparing_payment')}</p>
    </div>
  {:else if useIframe && paymentUrl}
    <div class="iframe-container">
      
      {#if !iframeLoaded}
        <div class="iframe-loading">
          <div class="spinner"></div>
          <p>{$_('checkout.loading_payment_form')}</p>
        </div>
      {/if}
      
      <iframe
        src={paymentUrl}
        title="AllPay Payment"
        class="payment-iframe"
        class:loaded={iframeLoaded}
        on:load={handleIframeLoad}
        on:error={handleIframeError}
        frameborder="0"
        scrolling="auto"
        allow="payment *"
      ></iframe>
    </div>
  {:else if paymentUrl}
    <div class="redirect-payment">
      <div class="payment-info">
        <h3>{$_('checkout.complete_payment')}</h3>
        <p>{$_('checkout.redirect_to_allpay')}</p>
        <div class="payment-details">
          <p><strong>{$_('checkout.amount')}:</strong> {cart.total_amount ? (cart.total_amount / 100).toFixed(2) : '0.00'} {cart.currency_code || 'ILS'}</p>
          <p><strong>{$_('checkout.payment_method')}:</strong> AllPay</p>
        </div>
      </div>
      
      <div class="payment-actions">
        <button type="button" on:click={onBack} class="btn-secondary">
          {$_('checkout.back')}
        </button>
        <button type="button" on:click={handleRedirectPayment} class="btn-primary">
          {$_('checkout.proceed_to_payment')}
        </button>
      </div>
    </div>
  {/if}
</div>

<style>
  .allpay-payment {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  }

  .error-message {
    background-color: #fee;
    border: 1px solid #fcc;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    text-align: center;
  }

  .error-message p {
    color: #c33;
    margin-bottom: 1rem;
  }

  .retry-button {
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    cursor: pointer;
  }

  .retry-button:hover {
    background-color: #0056b3;
  }

  .loading-state {
    text-align: center;
    padding: 2rem;
  }

  .spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .iframe-container {
    position: relative;
    width: 100%;
    min-height: 500px;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
  }

  .iframe-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #f9f9f9;
    z-index: 1;
  }

  .payment-iframe {
    width: 100%;
    height: 500px;
    border: none;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .payment-iframe.loaded {
    opacity: 1;
  }

  .redirect-payment {
    text-align: center;
    padding: 2rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #f9f9f9;
  }

  .payment-info h3 {
    margin-bottom: 1rem;
    color: #333;
  }

  .payment-details {
    margin: 1.5rem 0;
    padding: 1rem;
    background-color: white;
    border-radius: 4px;
    text-align: left;
  }

  .payment-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
  }

  .btn-primary, .btn-secondary {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s;
  }

  .btn-primary {
    background-color: #007bff;
    color: white;
  }

  .btn-primary:hover {
    background-color: #0056b3;
  }

  .btn-secondary {
    background-color: #6c757d;
    color: white;
  }

  .btn-secondary:hover {
    background-color: #545b62;
  }
</style>
