<script lang="ts">
  import { onMount } from 'svelte'
  import { goto } from '$app/navigation'
  import { _ } from '$lib/i18n'
  import { apiClient } from '$lib/api/client'

  let isVerifying = true
  let verificationError = ''
  let paymentDetails: any = null

  onMount(async () => {
    await verifyPayment()
  })

  async function verifyPayment() {
    try {
      // Get transaction details from session storage
      const cartId = sessionStorage.getItem('allpay_cart_id')
      const transactionId = sessionStorage.getItem('allpay_transaction_id')

      if (!transactionId) {
        throw new Error('No transaction ID found')
      }

      console.log('🔄 Verifying AllPay payment:', transactionId)

      // Verify payment status with backend
      const statusResponse = await apiClient.checkAllPayPaymentStatus(transactionId)
      
      if (statusResponse.data.status === 1) {
        console.log('✅ AllPay payment verified as successful')
        paymentDetails = statusResponse.data
        
        // Clear session storage
        sessionStorage.removeItem('allpay_cart_id')
        sessionStorage.removeItem('allpay_transaction_id')
        
        // Redirect to order confirmation after a short delay
        setTimeout(() => {
          if (cartId) {
            goto(`/orders/${cartId}/confirmation`)
          } else {
            goto('/orders')
          }
        }, 3000)
      } else {
        throw new Error('Payment verification failed')
      }
    } catch (error) {
      console.error('❌ AllPay payment verification failed:', error)
      verificationError = error.message || 'Payment verification failed'
    } finally {
      isVerifying = false
    }
  }

  function handleReturnToShop() {
    goto('/')
  }

  function handleViewOrders() {
    goto('/orders')
  }
</script>

<svelte:head>
  <title>{$_('checkout.payment_success')} - AllPay</title>
</svelte:head>

<div class="success-page">
  <div class="container">
    {#if isVerifying}
      <div class="verifying-state">
        <div class="spinner"></div>
        <h2>{$_('checkout.verifying_payment')}</h2>
        <p>{$_('checkout.please_wait_verification')}</p>
      </div>
    {:else if verificationError}
      <div class="error-state">
        <div class="error-icon">❌</div>
        <h2>{$_('checkout.payment_verification_failed')}</h2>
        <p>{verificationError}</p>
        <div class="actions">
          <button on:click={handleReturnToShop} class="btn-primary">
            {$_('checkout.return_to_shop')}
          </button>
          <button on:click={handleViewOrders} class="btn-secondary">
            {$_('checkout.view_orders')}
          </button>
        </div>
      </div>
    {:else if paymentDetails}
      <div class="success-state">
        <div class="success-icon">✅</div>
        <h2>{$_('checkout.payment_successful')}</h2>
        <p>{$_('checkout.payment_processed_successfully')}</p>
        
        <div class="payment-summary">
          <h3>{$_('checkout.payment_details')}</h3>
          <div class="detail-row">
            <span class="label">{$_('checkout.amount')}:</span>
            <span class="value">{paymentDetails.amount} {paymentDetails.currency}</span>
          </div>
          <div class="detail-row">
            <span class="label">{$_('checkout.payment_method')}:</span>
            <span class="value">AllPay</span>
          </div>
          {#if paymentDetails.card_brand && paymentDetails.card_mask}
            <div class="detail-row">
              <span class="label">{$_('checkout.card')}:</span>
              <span class="value">{paymentDetails.card_brand} ending in {paymentDetails.card_mask.slice(-4)}</span>
            </div>
          {/if}
          <div class="detail-row">
            <span class="label">{$_('checkout.transaction_id')}:</span>
            <span class="value">{paymentDetails.order_id}</span>
          </div>
        </div>

        <div class="redirect-notice">
          <p>{$_('checkout.redirecting_to_confirmation')}</p>
          <div class="countdown-spinner"></div>
        </div>

        <div class="actions">
          <button on:click={handleReturnToShop} class="btn-secondary">
            {$_('checkout.return_to_shop')}
          </button>
          <button on:click={handleViewOrders} class="btn-primary">
            {$_('checkout.view_orders')}
          </button>
        </div>
      </div>
    {/if}
  </div>
</div>

<style>
  .success-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    padding: 2rem;
  }

  .container {
    max-width: 600px;
    width: 100%;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 3rem;
    text-align: center;
  }

  .verifying-state, .error-state, .success-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
  }

  .spinner, .countdown-spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
  }

  .countdown-spinner {
    width: 20px;
    height: 20px;
    border-width: 2px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .success-icon {
    font-size: 4rem;
    color: #28a745;
  }

  .error-icon {
    font-size: 4rem;
    color: #dc3545;
  }

  h2 {
    margin: 0;
    color: #333;
    font-size: 1.8rem;
  }

  p {
    margin: 0;
    color: #666;
    font-size: 1.1rem;
  }

  .payment-summary {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 2rem 0;
    text-align: left;
  }

  .payment-summary h3 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1.2rem;
    text-align: center;
  }

  .detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
  }

  .detail-row:last-child {
    border-bottom: none;
  }

  .label {
    font-weight: 600;
    color: #495057;
  }

  .value {
    color: #333;
    font-family: monospace;
  }

  .redirect-notice {
    background-color: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 6px;
    padding: 1rem;
    margin: 1.5rem 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
  }

  .btn-primary, .btn-secondary {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-block;
  }

  .btn-primary {
    background-color: #007bff;
    color: white;
  }

  .btn-primary:hover {
    background-color: #0056b3;
  }

  .btn-secondary {
    background-color: #6c757d;
    color: white;
  }

  .btn-secondary:hover {
    background-color: #545b62;
  }

  @media (max-width: 768px) {
    .success-page {
      padding: 1rem;
    }

    .container {
      padding: 2rem;
    }

    .actions {
      flex-direction: column;
    }

    .btn-primary, .btn-secondary {
      width: 100%;
    }
  }
</style>
