# Book Detail Page Guide

This guide explains the new dedicated book page feature that displays individual book details with chapters and cart functionality.

## 🔗 URL Structure

The book page is accessible via:
- `/book/{book_id}` - Using the book's MedusaJS product ID
- `/book/{book_handle}` - Using the book's handle/slug if configured
- `/book/{book_title_slug}` - Using a URL-friendly version of the book title

## 📖 Page Layout

### Left Side: Book Image
- **Book cover image** from the product's `cover_image` field
- **Fallback book icon** if no image is available
- **Loading animation** while image loads
- **Responsive design** that scales properly on all devices

### Right Side: Book Details
- **Book title** prominently displayed
- **Book description** if available
- **Table of Contents section** with all chapters

## 📚 Table of Contents Features

### Chapter Display
Each chapter shows:
- **Chapter title** (localized based on current language)
- **Chapter description/preview content** (localized)
- **Chapter metadata**:
  - Duration (if available)
  - Difficulty level (if available)
- **Two action buttons**:
  - **Preview Button**: Links to `/chapters/{chapter_id}` for chapter preview
  - **Add to Cart Button**: Adds individual chapter to shopping cart

### Chapter Pricing
- **Individual chapter pricing** displayed on each "Add to Cart" button
- **Formatted pricing** using the current region's currency
- **Loading states** when adding chapters to cart

## 🛒 Cart Integration

### Individual Chapter Purchase
- Users can **add individual chapters** to their cart
- Each chapter becomes a separate cart item
- **Chapter metadata** is preserved (book_id, chapter_id)
- **Loading indicators** show when adding to cart

### Cart Item Structure
When a chapter is added to cart, it creates a product-like object:
```javascript
{
  id: `chapter-${chapter.id}`,
  title: chapter.title,
  price: chapter.price || 0,
  type: 'chapter',
  chapter_id: chapter.id,
  book_id: book.id
}
```

## 🌐 Multilingual Support

### Localized Content
- **Chapter titles** display in current language (Hebrew/English/Russian)
- **Chapter descriptions** show localized preview content
- **UI elements** are fully translated
- **RTL support** for Hebrew layout

### Translation Keys
The page uses these translation keys:
- `book.page_description` - Page meta description
- `book.table_of_contents` - Table of contents heading
- `book.preview` - Preview button text
- `book.add_to_cart` - Add to cart button text
- `book.no_chapters` - Message when no chapters available
- `book.loading_chapters` - Loading message
- `common.loading` - General loading message
- `common.minutes` - Minutes unit
- `common.difficulty` - Difficulty label
- `common.book_cover` - Book cover alt text
- `common.adding` - Adding to cart state

## 🔗 Navigation Integration

### From Main Page
The main page's "View Book Details" button now links to the book page:
```javascript
on:click={() => {
  if (selectedProduct) {
    goto(`/book/${selectedProduct.id}`)
  }
}}
```

### To Chapter Preview
Each chapter's preview button links to the chapter detail page:
```javascript
on:click={() => handleChapterPreview(chapter.id)}
// Navigates to: /chapters/{chapter.id}
```

## 📱 Responsive Design

### Mobile Layout
- **Single column layout** on mobile devices
- **Book image** displayed above details
- **Touch-friendly buttons** with proper spacing
- **Optimized text sizes** for mobile reading

### Desktop Layout
- **Two-column grid** with image on left, details on right
- **Larger book image** for better visual impact
- **Side-by-side button layout** for chapter actions

## 🎨 Styling

### Design System Integration
- Uses **existing CSS custom properties** for colors and fonts
- **Consistent card styling** with `card-classic` class
- **Button styles** match the site's design system:
  - `btn-secondary` for preview buttons
  - `btn-primary` for add to cart buttons
- **Hover effects** and transitions for better UX

### Loading States
- **Skeleton loading** for book image
- **Animated loading** for chapters list
- **Button loading states** when adding to cart
- **Disabled states** to prevent double-clicks

## 🔧 Technical Implementation

### Route Structure
```
frontend/src/routes/book/[slug]/+page.svelte
```

### Data Loading
1. **Products loaded** from store on mount
2. **Book found** by matching slug to product ID, handle, or title
3. **Chapters loaded** automatically when book is found
4. **Real-time updates** when data changes

### Error Handling
- **Book not found** - Shows loading state indefinitely (could be enhanced)
- **No chapters** - Shows "no chapters available" message
- **Image loading errors** - Falls back to book icon
- **Cart errors** - Logged to console (could show user feedback)

## 🚀 Usage Examples

### Accessing a Book Page
```
# By product ID
https://yoursite.com/book/prod_123abc

# By handle (if configured)
https://yoursite.com/book/hebrew-grammar-book

# By title slug
https://yoursite.com/book/hebrew-grammar-for-beginners
```

### Integration with Existing Features
- **Cart system** - Chapters added to existing cart
- **Authentication** - Works with logged-in and guest users
- **Pricing** - Uses current region's currency
- **Language** - Respects current language setting

## 🔮 Future Enhancements

### Potential Improvements
1. **Book not found page** - Better error handling
2. **Chapter bundles** - Option to buy multiple chapters at once
3. **Progress tracking** - Show user's progress through chapters
4. **Reviews/ratings** - User feedback on chapters
5. **Related books** - Suggestions for similar content
6. **Social sharing** - Share book or chapters
7. **Bookmarks** - Save favorite chapters
8. **Search within book** - Find specific content

### SEO Enhancements
1. **Dynamic meta tags** - Book-specific titles and descriptions
2. **Structured data** - Rich snippets for search engines
3. **Open Graph tags** - Better social media sharing
4. **Canonical URLs** - Proper URL structure

---

The book detail page provides a comprehensive view of each book with easy access to individual chapters and seamless cart integration, following the site's existing design patterns and user experience principles.
